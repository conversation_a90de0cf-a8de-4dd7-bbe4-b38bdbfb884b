import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { User, Shield, AlertTriangle } from "lucide-react";
import { User as UserType } from "@/types/api";

interface EditRoleModalProps {
  user: UserType | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (userId: number, newRole: string, reason?: string) => void;
  isLoading?: boolean;
}

const EditRoleModal = ({ user, isOpen, onClose, onSave, isLoading = false }: EditRoleModalProps) => {
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [reason, setReason] = useState<string>("");

  if (!user) return null;

  const roles = [
    {
      value: "ADMIN",
      label: "Administrator",
      description: "Full system access and management",
      color: "bg-purple-100 text-purple-800",
      statusAfterChange: "ACTIVE",
      permissions: "Complete system control, user management, settings"
    },
    {
      value: "DOCTOR",
      label: "Doctor",
      description: "Medical professional access",
      color: "bg-blue-100 text-blue-800",
      statusAfterChange: "PENDING_APPROVAL",
      permissions: "Patient management, appointments, medical records"
    },
    {
      value: "CLINIC",
      label: "Clinic",
      description: "Clinic management access",
      color: "bg-green-100 text-green-800",
      statusAfterChange: "PENDING_APPROVAL",
      permissions: "Clinic operations, staff management, appointments"
    },
    {
      value: "CLINIC_STAFF",
      label: "Clinic Staff",
      description: "Clinic staff member access",
      color: "bg-teal-100 text-teal-800",
      statusAfterChange: "ACTIVE",
      permissions: "Appointment scheduling, basic patient info"
    },
    {
      value: "PATIENT",
      label: "Patient",
      description: "Patient portal access",
      color: "bg-gray-100 text-gray-800",
      statusAfterChange: "ACTIVE",
      permissions: "View appointments, medical records, book appointments"
    },
  ];

  const handleSave = () => {
    if (selectedRole && selectedRole !== user.role) {
      onSave(user.id, selectedRole, reason);
      setSelectedRole("");
      setReason("");
    }
  };

  const currentRole = roles.find(role => role.value === user.role);
  const newRole = roles.find(role => role.value === selectedRole);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Edit User Role
          </DialogTitle>
          <DialogDescription>
            Change the role and permissions for {user.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current User Info */}
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <User className="h-8 w-8 text-gray-500" />
              <div>
                <p className="font-medium">{user.name}</p>
                <p className="text-sm text-gray-600">{user.email}</p>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Current Role</Label>
              <div className="mt-1">
                <Badge className={currentRole?.color}>
                  {currentRole?.label}
                </Badge>
                <p className="text-xs text-gray-500 mt-1">{currentRole?.description}</p>
              </div>
            </div>
          </div>

          {/* Role Selection */}
          <div className="space-y-3">
            <Label htmlFor="role">New Role</Label>
            <Select value={selectedRole} onValueChange={setSelectedRole}>
              <SelectTrigger>
                <SelectValue placeholder="Select a new role" />
              </SelectTrigger>
              <SelectContent>
                {roles.map((role) => (
                  <SelectItem key={role.value} value={role.value} disabled={role.value === user.role}>
                    <div className="flex flex-col gap-1 py-1">
                      <div className="flex items-center gap-2">
                        <Badge className={role.color} variant="outline">
                          {role.label}
                        </Badge>
                        {role.value === user.role && (
                          <span className="text-xs text-gray-500">(Current)</span>
                        )}
                      </div>
                      <span className="text-xs text-gray-600">{role.description}</span>
                      <span className="text-xs text-blue-600">
                        Status after change: {role.statusAfterChange.replace('_', ' ')}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Selected Role Details */}
          {selectedRole && selectedRole !== user.role && (
            <div className="space-y-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900">Role Change Details</h4>
              {(() => {
                const newRole = roles.find(role => role.value === selectedRole);
                return newRole ? (
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium text-blue-800">New Permissions: </span>
                      <span className="text-blue-700">{newRole.permissions}</span>
                    </div>
                    <div>
                      <span className="font-medium text-blue-800">Status After Change: </span>
                      <Badge className={
                        newRole.statusAfterChange === 'ACTIVE'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }>
                        {newRole.statusAfterChange.replace('_', ' ')}
                      </Badge>
                    </div>
                    {newRole.statusAfterChange === 'PENDING_APPROVAL' && (
                      <div className="text-amber-700 text-xs">
                        ⚠️ This role requires admin approval before the user can access the system.
                      </div>
                    )}
                  </div>
                ) : null;
              })()}
            </div>
          )}

          {/* Reason Field */}
          {selectedRole && selectedRole !== user.role && (
            <div className="space-y-3">
              <Label htmlFor="reason">Reason for Role Change</Label>
              <textarea
                id="reason"
                placeholder="Enter the reason for this role change (optional)"
                className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                value={reason}
                onChange={(e) => setReason(e.target.value)}
              />
              <p className="text-xs text-gray-500">
                This reason will be logged for audit purposes.
              </p>
            </div>
          )}

          {/* Warning for role change */}
          {selectedRole && selectedRole !== user.role && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-800">Role Change Warning</p>
                  <p className="text-yellow-700">
                    Changing from <strong>{currentRole?.label}</strong> to <strong>{newRole?.label}</strong> will
                    immediately update the user's permissions and access level.
                  </p>
                  {(() => {
                    const targetRole = roles.find(role => role.value === selectedRole);
                    if (targetRole?.statusAfterChange === 'PENDING_APPROVAL') {
                      return (
                        <p className="text-yellow-700 mt-1">
                          <strong>Note:</strong> The user's status will change to <strong>PENDING_APPROVAL</strong> and
                          they will need admin approval before accessing the system.
                        </p>
                      );
                    } else {
                      return (
                        <p className="text-yellow-700 mt-1">
                          <strong>Note:</strong> The user will have immediate access with their new role permissions.
                        </p>
                      );
                    }
                  })()}
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={!selectedRole || selectedRole === user.role || isLoading}
            >
              {isLoading ? "Updating..." : "Update Role"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditRoleModal;
