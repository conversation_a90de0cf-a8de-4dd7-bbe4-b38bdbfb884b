
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Building2, Users, Calendar, Activity } from "lucide-react";

const ClinicDashboard = () => {
  const { user } = useAuth();

  const stats = [
    { title: "Total Staff", value: "24", icon: Users, color: "text-blue-600" },
    { title: "Today's Appointments", value: "45", icon: Calendar, color: "text-green-600" },
    { title: "Active Doctors", value: "8", icon: Activity, color: "text-orange-600" },
    { title: "Departments", value: "6", icon: Building2, color: "text-purple-600" }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Clinic Dashboard</h1>
          <p className="text-gray-600">Welcome back, {user?.name}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Staff Overview</CardTitle>
              <CardDescription>Current staff status</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">All staff members are on duty.</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Clinic Performance</CardTitle>
              <CardDescription>Monthly metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Performance metrics will be displayed here.</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ClinicDashboard;
