import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export interface ReportData {
  title: string;
  generatedAt: string;
  generatedBy: string;
  summary: {
    totalUsers: number;
    totalAppointments: number;
    activeClinics: number;
    systemUptime: string;
  };
  userGrowthData: Array<{
    month: string;
    patients: number;
    doctors: number;
    clinics: number;
  }>;
  appointmentData: Array<{
    name: string;
    value: number;
    percentage: string;
  }>;
  performanceMetrics: {
    apiResponseTime: string;
    databaseQueryTime: string;
    errorRate: string;
    activeSessions: string;
  };
  revenueMetrics: {
    monthlyRecurringRevenue: string;
    totalSubscriptionRevenue: string;
    averageRevenuePerUser: string;
    churnRate: string;
  };
}

export class PDFExportService {
  private doc: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  private margin: number = 20;
  private currentY: number = 20;

  constructor() {
    this.doc = new jsPDF();
    this.pageWidth = this.doc.internal.pageSize.getWidth();
    this.pageHeight = this.doc.internal.pageSize.getHeight();
  }

  private addHeader(title: string, subtitle?: string) {
    // Add logo/header background
    this.doc.setFillColor(59, 130, 246); // Blue background
    this.doc.rect(0, 0, this.pageWidth, 40, 'F');

    // Add title
    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(24);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(title, this.margin, 25);

    if (subtitle) {
      this.doc.setFontSize(12);
      this.doc.setFont('helvetica', 'normal');
      this.doc.text(subtitle, this.margin, 35);
    }

    this.currentY = 50;
    this.doc.setTextColor(0, 0, 0); // Reset to black
  }

  private addSection(title: string) {
    this.checkPageBreak(30);

    this.doc.setFontSize(16);
    this.doc.setFont('helvetica', 'bold');
    this.doc.setTextColor(59, 130, 246);
    this.doc.text(title, this.margin, this.currentY);

    // Add underline
    this.doc.setDrawColor(59, 130, 246);
    this.doc.line(this.margin, this.currentY + 2, this.margin + 60, this.currentY + 2);

    this.currentY += 15;
    this.doc.setTextColor(0, 0, 0);
  }

  private addText(text: string, fontSize: number = 11, style: 'normal' | 'bold' = 'normal') {
    this.checkPageBreak(15);

    this.doc.setFontSize(fontSize);
    this.doc.setFont('helvetica', style);
    this.doc.text(text, this.margin, this.currentY);
    this.currentY += fontSize * 0.5 + 5;
  }

  private addKeyValuePair(key: string, value: string, indent: number = 0) {
    this.checkPageBreak(15);

    this.doc.setFontSize(11);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(key + ':', this.margin + indent, this.currentY);

    this.doc.setFont('helvetica', 'normal');
    this.doc.text(value, this.margin + indent + 60, this.currentY);

    this.currentY += 12;
  }

  private addTable(headers: string[], data: string[][], title?: string) {
    this.checkPageBreak(50);

    if (title) {
      this.doc.setFontSize(14);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text(title, this.margin, this.currentY);
      this.currentY += 15;
    }

    // Calculate column widths
    const tableWidth = this.pageWidth - 2 * this.margin;
    const colWidth = tableWidth / headers.length;

    // Draw header
    this.doc.setFillColor(59, 130, 246);
    this.doc.rect(this.margin, this.currentY, tableWidth, 12, 'F');

    this.doc.setTextColor(255, 255, 255);
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'bold');

    headers.forEach((header, index) => {
      this.doc.text(header, this.margin + (index * colWidth) + 5, this.currentY + 8);
    });

    this.currentY += 12;
    this.doc.setTextColor(0, 0, 0);
    this.doc.setFont('helvetica', 'normal');

    // Draw data rows
    data.forEach((row, rowIndex) => {
      this.checkPageBreak(12);

      // Alternate row colors
      if (rowIndex % 2 === 0) {
        this.doc.setFillColor(248, 250, 252);
        this.doc.rect(this.margin, this.currentY, tableWidth, 12, 'F');
      }

      row.forEach((cell, colIndex) => {
        this.doc.text(cell.toString(), this.margin + (colIndex * colWidth) + 5, this.currentY + 8);
      });

      this.currentY += 12;
    });

    this.currentY += 10;
  }

  private checkPageBreak(requiredSpace: number) {
    if (this.currentY + requiredSpace > this.pageHeight - this.margin) {
      this.doc.addPage();
      this.currentY = this.margin;
    }
  }

  private addFooter() {
    const pageCount = this.doc.getNumberOfPages();

    for (let i = 1; i <= pageCount; i++) {
      this.doc.setPage(i);

      // Add footer line
      this.doc.setDrawColor(200, 200, 200);
      this.doc.line(this.margin, this.pageHeight - 25, this.pageWidth - this.margin, this.pageHeight - 25);

      // Add page number and generation info
      this.doc.setFontSize(9);
      this.doc.setFont('helvetica', 'normal');
      this.doc.setTextColor(100, 100, 100);

      this.doc.text(
        `Page ${i} of ${pageCount}`,
        this.pageWidth - this.margin - 30,
        this.pageHeight - 15
      );

      this.doc.text(
        'Generated by MediConnect Admin System',
        this.margin,
        this.pageHeight - 15
      );
    }
  }

  public async generateSystemReport(data: ReportData): Promise<void> {
    // Header
    this.addHeader('MediConnect System Reports', `Generated on ${data.generatedAt}`);

    // Report Information
    this.addSection('Report Information');
    this.addKeyValuePair('Report Title', data.title);
    this.addKeyValuePair('Generated At', data.generatedAt);
    this.addKeyValuePair('Generated By', data.generatedBy);

    // Executive Summary
    this.addSection('Executive Summary');
    this.addKeyValuePair('Total Users', data.summary.totalUsers.toLocaleString());
    this.addKeyValuePair('Total Appointments', data.summary.totalAppointments.toLocaleString());
    this.addKeyValuePair('Active Clinics', data.summary.activeClinics.toString());
    this.addKeyValuePair('System Uptime', data.summary.systemUptime);

    // User Growth Data
    this.addSection('User Growth Analysis');
    const userGrowthHeaders = ['Month', 'Patients', 'Doctors', 'Clinics', 'Total'];
    const userGrowthData = data.userGrowthData.map(item => [
      item.month,
      item.patients.toString(),
      item.doctors.toString(),
      item.clinics.toString(),
      (item.patients + item.doctors + item.clinics).toString()
    ]);
    this.addTable(userGrowthHeaders, userGrowthData, 'Monthly User Registration');

    // Appointment Statistics
    this.addSection('Appointment Analytics');
    const appointmentHeaders = ['Status', 'Count', 'Percentage'];
    const appointmentTableData = data.appointmentData.map(item => [
      item.name,
      item.value.toString(),
      item.percentage
    ]);
    this.addTable(appointmentHeaders, appointmentTableData, 'Appointment Status Distribution');

    // Performance Metrics
    this.addSection('System Performance');
    this.addKeyValuePair('API Response Time', data.performanceMetrics.apiResponseTime);
    this.addKeyValuePair('Database Query Time', data.performanceMetrics.databaseQueryTime);
    this.addKeyValuePair('Error Rate', data.performanceMetrics.errorRate);
    this.addKeyValuePair('Active Sessions', data.performanceMetrics.activeSessions);

    // Revenue Metrics
    this.addSection('Revenue Analytics');
    this.addKeyValuePair('Monthly Recurring Revenue', data.revenueMetrics.monthlyRecurringRevenue);
    this.addKeyValuePair('Total Subscription Revenue', data.revenueMetrics.totalSubscriptionRevenue);
    this.addKeyValuePair('Average Revenue Per User', data.revenueMetrics.averageRevenuePerUser);
    this.addKeyValuePair('Churn Rate', data.revenueMetrics.churnRate);

    // Add footer
    this.addFooter();

    // Save the PDF
    const fileName = `MediConnect_System_Report_${new Date().toISOString().split('T')[0]}.pdf`;
    this.doc.save(fileName);
  }

  public async generateUserReport(users: Array<{
    id: number;
    name: string;
    email: string;
    role: string;
    status: string;
    createdAt: string;
  }>): Promise<void> {
    this.addHeader('MediConnect User Report', `Generated on ${new Date().toLocaleDateString()}`);

    // Summary
    this.addSection('User Summary');
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.status === 'ACTIVE').length;
    const pendingUsers = users.filter(u => u.status === 'PENDING_APPROVAL').length;
    const inactiveUsers = users.filter(u => u.status === 'INACTIVE').length;

    this.addKeyValuePair('Total Users', totalUsers.toString());
    this.addKeyValuePair('Active Users', activeUsers.toString());
    this.addKeyValuePair('Pending Approval', pendingUsers.toString());
    this.addKeyValuePair('Inactive Users', inactiveUsers.toString());

    // User Details Table
    this.addSection('User Details');
    const userHeaders = ['Name', 'Email', 'Role', 'Status', 'Created Date'];
    const userData = users.map(user => [
      user.name,
      user.email,
      user.role,
      user.status,
      new Date(user.createdAt).toLocaleDateString()
    ]);

    this.addTable(userHeaders, userData, 'Complete User List');

    // Role Distribution
    this.addSection('Role Distribution');
    const roleStats = users.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    Object.entries(roleStats).forEach(([role, count]) => {
      this.addKeyValuePair(role, count.toString());
    });

    this.addFooter();

    const fileName = `MediConnect_User_Report_${new Date().toISOString().split('T')[0]}.pdf`;
    this.doc.save(fileName);
  }

  public async captureChartAsPDF(elementId: string, title: string): Promise<void> {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error(`Element with ID ${elementId} not found`);
    }

    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
    });

    this.addHeader(title, `Generated on ${new Date().toLocaleDateString()}`);

    const imgData = canvas.toDataURL('image/png');
    const imgWidth = this.pageWidth - 2 * this.margin;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    this.doc.addImage(imgData, 'PNG', this.margin, this.currentY, imgWidth, imgHeight);

    this.addFooter();

    const fileName = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
    this.doc.save(fileName);
  }
}

export const pdfExportService = new PDFExportService();
