# 🔧 Backend Reschedule Endpoint Requirements

## ❌ Current Issue
The frontend is getting a 500 error when trying to reschedule appointments:
```
PUT http://localhost:8083/api/appointments/148 500 (Internal Server Error)
{"success":false,"message":"An unexpected error occurred. Please try again later.","data":null}
```

## ✅ Required Backend Implementation

### **Option 1: Dedicated Reschedule Endpoint (Recommended)**
```java
@PutMapping("/api/appointments/{appointmentId}/reschedule")
public ResponseEntity<?> rescheduleAppointment(
    @PathVariable Long appointmentId,
    @RequestBody RescheduleRequest request) {
    
    try {
        // Validate appointment exists and user has permission
        Appointment appointment = appointmentRepository.findById(appointmentId)
            .orElseThrow(() -> new EntityNotFoundException("Appointment not found"));
        
        // Validate new appointment date/time
        LocalDateTime newDateTime = LocalDateTime.parse(request.getAppointmentDate());
        
        // Check for conflicts
        boolean hasConflict = appointmentRepository.existsByDoctorIdAndAppointmentDateAndIdNot(
            appointment.getDoctorId(), newDateTime, appointmentId);
        
        if (hasConflict) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "The selected time slot is not available"
            ));
        }
        
        // Update appointment
        appointment.setAppointmentDate(newDateTime);
        if (request.getNotes() != null) {
            appointment.setNotes(request.getNotes());
        }
        appointment.setUpdatedAt(LocalDateTime.now());
        
        appointmentRepository.save(appointment);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Appointment rescheduled successfully",
            "data", appointment
        ));
        
    } catch (Exception e) {
        return ResponseEntity.status(500).body(Map.of(
            "success", false,
            "message", "Failed to reschedule appointment: " + e.getMessage()
        ));
    }
}
```

### **Option 2: General Update Endpoint**
```java
@PutMapping("/api/appointments/{appointmentId}")
public ResponseEntity<?> updateAppointment(
    @PathVariable Long appointmentId,
    @RequestBody UpdateAppointmentRequest request) {
    
    try {
        Appointment appointment = appointmentRepository.findById(appointmentId)
            .orElseThrow(() -> new EntityNotFoundException("Appointment not found"));
        
        // Update fields if provided
        if (request.getAppointmentDate() != null) {
            LocalDateTime newDateTime = LocalDateTime.parse(request.getAppointmentDate());
            
            // Check for conflicts if date is changing
            if (!newDateTime.equals(appointment.getAppointmentDate())) {
                boolean hasConflict = appointmentRepository.existsByDoctorIdAndAppointmentDateAndIdNot(
                    appointment.getDoctorId(), newDateTime, appointmentId);
                
                if (hasConflict) {
                    return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "message", "The selected time slot is not available"
                    ));
                }
            }
            
            appointment.setAppointmentDate(newDateTime);
        }
        
        if (request.getNotes() != null) {
            appointment.setNotes(request.getNotes());
        }
        
        if (request.getStatus() != null) {
            appointment.setStatus(AppointmentStatus.valueOf(request.getStatus()));
        }
        
        appointment.setUpdatedAt(LocalDateTime.now());
        appointmentRepository.save(appointment);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Appointment updated successfully",
            "data", appointment
        ));
        
    } catch (Exception e) {
        return ResponseEntity.status(500).body(Map.of(
            "success", false,
            "message", "Failed to update appointment: " + e.getMessage()
        ));
    }
}
```

## 📋 Request/Response Format

### **Request Body**
```json
{
  "appointmentDate": "2025-01-15T14:30:00",
  "notes": "Rescheduled due to patient request"
}
```

### **Success Response**
```json
{
  "success": true,
  "message": "Appointment rescheduled successfully",
  "data": {
    "id": 148,
    "appointmentDate": "2025-01-15T14:30:00",
    "notes": "Rescheduled due to patient request",
    "updatedAt": "2025-01-10T10:30:00"
  }
}
```

### **Error Response (Conflict)**
```json
{
  "success": false,
  "message": "The selected time slot is not available",
  "data": null
}
```

### **Error Response (Not Found)**
```json
{
  "success": false,
  "message": "Appointment not found",
  "data": null
}
```

## 🔍 Required Validations

### **1. Appointment Existence**
- Verify appointment exists in database
- Return 404 if appointment not found

### **2. Permission Check**
- Verify user has permission to modify appointment
- Check doctor ownership or admin role

### **3. Date/Time Validation**
- Validate appointmentDate format (ISO 8601)
- Ensure date is in the future
- Check against doctor's working hours

### **4. Conflict Detection**
- Check for existing appointments at same date/time
- Consider appointment duration for overlap detection
- Exclude current appointment from conflict check

### **5. Business Rules**
- Don't allow rescheduling completed appointments
- Don't allow rescheduling cancelled appointments
- Validate minimum notice period if required

## 🗄️ Database Schema Requirements

### **Appointments Table**
```sql
-- Ensure these columns exist and are properly indexed
ALTER TABLE appointments ADD INDEX idx_doctor_date (doctor_id, appointment_date);
ALTER TABLE appointments ADD INDEX idx_appointment_date (appointment_date);
```

### **Required Fields**
- `id` (Primary Key)
- `doctor_id` (Foreign Key)
- `appointment_date` (DateTime)
- `notes` (Text, nullable)
- `status` (Enum)
- `updated_at` (DateTime)

## 🚀 Testing Endpoints

### **Test Cases**
1. **Valid Reschedule**: Update to available time slot
2. **Conflict Detection**: Try to reschedule to occupied slot
3. **Invalid Date**: Try to reschedule to past date
4. **Not Found**: Try to reschedule non-existent appointment
5. **Permission**: Try to reschedule another doctor's appointment

### **Test Data**
```bash
# Valid reschedule
curl -X PUT http://localhost:8083/api/appointments/148 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "appointmentDate": "2025-01-15T14:30:00",
    "notes": "Rescheduled due to patient request"
  }'

# Conflict test
curl -X PUT http://localhost:8083/api/appointments/148 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "appointmentDate": "2025-01-10T09:00:00"
  }'
```

## 📝 Implementation Priority

### **High Priority**
1. ✅ Fix the 500 error for basic update functionality
2. ✅ Implement conflict detection
3. ✅ Add proper error responses

### **Medium Priority**
1. ⚠️ Add working hours validation
2. ⚠️ Implement minimum notice period
3. ⚠️ Add appointment duration consideration

### **Low Priority**
1. 📋 Add audit logging for reschedules
2. 📋 Send notifications to patients
3. 📋 Add bulk reschedule functionality

## 🎯 Frontend Integration

The frontend is already prepared to handle:
- ✅ Multiple endpoint attempts (reschedule + update)
- ✅ Proper error handling and user feedback
- ✅ Automatic cache invalidation after successful reschedule
- ✅ Professional loading states and error messages
- ✅ Fallback to existing doctor service methods

Once the backend endpoint is implemented, the frontend will automatically work with the new reschedule functionality!
