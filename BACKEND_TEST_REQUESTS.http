# Backend API Test Requests
# Use these requests to test your Spring Boot backend endpoints

### Variables
@baseUrl = http://localhost:8083/api
@token = YOUR_JWT_TOKEN_HERE

### 1. Test Login
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "test123"
}

### 2. Get All Users (Admin)
GET {{baseUrl}}/admin/users
Authorization: Bearer {{token}}

### 3. Get Single User
GET {{baseUrl}}/admin/users/1
Authorization: Bearer {{token}}

### 4. Create New User
POST {{baseUrl}}/admin/users
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Dr. Test User",
  "email": "<EMAIL>",
  "password": "secure123",
  "phoneNumber": "+**********",
  "address": "123 Medical Center Dr",
  "dateOfBirth": "1985-03-15",
  "gender": "MALE",
  "role": "DOCTOR"
}

### 5. Update User Role (Request Body Method)
PUT {{baseUrl}}/admin/users/1/role
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "newRole": "ADMIN",
  "reason": "Promotion to administrator role"
}

### 6. Update User Role (Query Parameter Method)
PUT {{baseUrl}}/admin/users/1/change-role?newRole=PATIENT
Authorization: Bearer {{token}}

### 7. Activate User
PUT {{baseUrl}}/admin/users/1/activate
Authorization: Bearer {{token}}

### 8. Deactivate User
PUT {{baseUrl}}/admin/users/1/deactivate
Authorization: Bearer {{token}}

### 9. Test CORS (Options Request)
OPTIONS {{baseUrl}}/admin/users
Origin: http://localhost:5173
Access-Control-Request-Method: GET
Access-Control-Request-Headers: Authorization

### 10. Test Invalid Login
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}

### 11. Test Duplicate Email
POST {{baseUrl}}/admin/users
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Duplicate User",
  "email": "<EMAIL>",
  "password": "test123",
  "gender": "OTHER",
  "role": "PATIENT"
}

### 12. Test Invalid Role Change (Same Role)
PUT {{baseUrl}}/admin/users/1/role
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "newRole": "ADMIN",
  "reason": "Testing same role change"
}

### 13. Test Unauthorized Access (No Token)
GET {{baseUrl}}/admin/users

### 14. Test User Not Found
GET {{baseUrl}}/admin/users/999999
Authorization: Bearer {{token}}

### 15. Test Create Patient (Should be ACTIVE)
POST {{baseUrl}}/admin/users
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "John Patient",
  "email": "<EMAIL>",
  "password": "patient123",
  "phoneNumber": "+**********",
  "gender": "MALE",
  "role": "PATIENT"
}

### 16. Test Create Doctor (Should be PENDING_APPROVAL)
POST {{baseUrl}}/admin/users
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Dr. Sarah Wilson",
  "email": "<EMAIL>",
  "password": "doctor123",
  "phoneNumber": "+**********",
  "gender": "FEMALE",
  "role": "DOCTOR"
}

### 17. Test Create Clinic Staff (Should be ACTIVE)
POST {{baseUrl}}/admin/users
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Mary Staff",
  "email": "<EMAIL>",
  "password": "staff123",
  "phoneNumber": "+**********",
  "gender": "FEMALE",
  "role": "CLINIC_STAFF"
}

### 18. Test Role Change Patient to Doctor (ACTIVE → PENDING_APPROVAL)
PUT {{baseUrl}}/admin/users/2/role
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "newRole": "DOCTOR",
  "reason": "User qualified as medical professional"
}

### 19. Test Role Change Doctor to Patient (PENDING_APPROVAL → ACTIVE)
PUT {{baseUrl}}/admin/users/3/role
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "newRole": "PATIENT",
  "reason": "Role change back to patient"
}

### 20. Test System Statistics (For PDF Export)
GET {{baseUrl}}/admin/reports/system-stats
Authorization: Bearer {{token}}

### 21. Test System Statistics with Date Range
GET {{baseUrl}}/admin/reports/system-stats?days=7
Authorization: Bearer {{token}}

### 22. Test User Growth Data
GET {{baseUrl}}/admin/reports/user-growth
Authorization: Bearer {{token}}

### 23. Test User Growth Data with Custom Months
GET {{baseUrl}}/admin/reports/user-growth?months=6
Authorization: Bearer {{token}}

### 24. Test Performance Metrics
GET {{baseUrl}}/admin/reports/performance-metrics
Authorization: Bearer {{token}}

### 25. Test Appointment Analytics (If Available)
GET {{baseUrl}}/admin/reports/appointment-analytics
Authorization: Bearer {{token}}

### 26. Test Revenue Analytics (If Available)
GET {{baseUrl}}/admin/reports/revenue-analytics
Authorization: Bearer {{token}}

### 27. Health Check (Optional)
GET {{baseUrl}}/health
