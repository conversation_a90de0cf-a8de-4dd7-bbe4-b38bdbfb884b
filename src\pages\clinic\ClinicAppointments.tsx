
import { Card, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Clock, User, Filter } from "lucide-react";

const ClinicAppointments = () => {
  const todayAppointments = [
    {
      id: 1,
      patient: "<PERSON>",
      doctor: "Dr. <PERSON>",
      time: "09:00 AM",
      type: "Cardiology",
      status: "confirmed",
      room: "Room 101"
    },
    {
      id: 2,
      patient: "<PERSON>",
      doctor: "Dr. <PERSON>",
      time: "10:30 AM",
      type: "Neurology",
      status: "in-progress",
      room: "Room 203"
    }
  ];

  const upcomingAppointments = [
    {
      id: 3,
      patient: "<PERSON>",
      doctor: "Dr. <PERSON>",
      date: "Tomorrow",
      time: "11:00 AM",
      type: "Cardiology",
      status: "scheduled"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Clinic Appointments</h1>
              <p className="text-gray-600">Monitor and manage all clinic appointments</p>
            </div>
            <Button>
              <Filter className="h-4 w-4 mr-2" />
              Filter Appointments
            </Button>
          </div>
        </div>

        <Tabs defaultValue="today" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="today">Today</TabsTrigger>
            <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="today" className="space-y-4">
            {todayAppointments.map((appointment) => (
              <Card key={appointment.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{appointment.patient}</CardTitle>
                    <Badge className={getStatusColor(appointment.status)}>
                      {appointment.status}
                    </Badge>
                  </div>
                  <CardDescription>
                    with {appointment.doctor} • {appointment.type}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-6">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-gray-500" />
                        <span>{appointment.time}</span>
                      </div>
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1 text-gray-500" />
                        <span>{appointment.room}</span>
                      </div>
                    </div>
                    <div className="space-x-2">
                      <Button size="sm" variant="outline">View Details</Button>
                      <Button size="sm">Update Status</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="upcoming" className="space-y-4">
            {upcomingAppointments.map((appointment) => (
              <Card key={appointment.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{appointment.patient}</CardTitle>
                    <Badge className={getStatusColor(appointment.status)}>
                      {appointment.status}
                    </Badge>
                  </div>
                  <CardDescription>
                    with {appointment.doctor} • {appointment.type}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-6">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                        <span>{appointment.date}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-gray-500" />
                        <span>{appointment.time}</span>
                      </div>
                    </div>
                    <div className="space-x-2">
                      <Button size="sm" variant="outline">Reschedule</Button>
                      <Button size="sm">Confirm</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <p className="text-gray-600 text-center">No completed appointments to display</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ClinicAppointments;
