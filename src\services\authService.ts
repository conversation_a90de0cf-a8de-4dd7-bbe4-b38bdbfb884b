
import { api } from './api';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: {
    id: number;
    name: string;
    email: string;
    role: string;
    status: string;
  };
  message?: string;
}

export const authService = {
  // Token management
  TOKEN_KEY: 'mediconnect_token',
  USER_KEY: 'mediconnect_user',

  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await api.post('/auth/login', credentials);

      if (response.token) {
        // Store token and user data
        localStorage.setItem(this.TOKEN_KEY, response.token);
        localStorage.setItem(this.USER_KEY, JSON.stringify(response.user));
      }

      return response;
    } catch (error: any) {
      console.error('Login failed:', error);
      throw new Error(error.message || 'Login failed. Please check your credentials.');
    }
  },

  async register(userData: any) {
    try {
      const response = await api.post('/auth/register', userData);

      if (response.token) {
        // Store token and user data
        localStorage.setItem(this.TOKEN_KEY, response.token);
        localStorage.setItem(this.USER_KEY, JSON.stringify(response.user));
      }

      return response;
    } catch (error: any) {
      console.error('Registration failed:', error);
      throw new Error(error.message || 'Registration failed. Please try again.');
    }
  },

  async logout() {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.warn('Logout endpoint not available');
    } finally {
      // Always clear local storage
      this.clearAuthData();
    }
  },

  async validateToken() {
    try {
      const response = await api.post('/auth/validate');
      return response.valid === true;
    } catch (error) {
      console.warn('Token validation failed');
      this.clearAuthData();
      return false;
    }
  },

  async refreshToken() {
    try {
      const response = await api.post('/auth/refresh');

      if (response.token) {
        localStorage.setItem(this.TOKEN_KEY, response.token);
        return response.token;
      }

      return null;
    } catch (error) {
      console.warn('Token refresh failed');
      this.clearAuthData();
      return null;
    }
  },

  // Utility methods
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  },

  getUser(): any | null {
    const userData = localStorage.getItem(this.USER_KEY);
    return userData ? JSON.parse(userData) : null;
  },

  isAuthenticated(): boolean {
    const token = this.getToken();
    const user = this.getUser();
    return !!(token && user);
  },

  clearAuthData(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  },

  // Role checking
  hasRole(role: string): boolean {
    const user = this.getUser();
    return user?.role === role;
  },

  isAdmin(): boolean {
    return this.hasRole('ADMIN');
  }
};

// Test function for your backend API
export const testBackendConnection = async () => {
  try {
    console.log('🔍 Testing Backend API Connection...');
    console.log('📍 API Base URL:', 'http://localhost:8083/api');

    // Test login with your test credentials
    const loginResponse = await authService.login({
      email: "<EMAIL>",
      password: "test123"
    });

    console.log('✅ Login successful:', loginResponse);

    // Test getting users (admin endpoint)
    if (authService.isAdmin()) {
      const users = await api.get('/admin/users');
      console.log('✅ Users fetched:', users);

      // Test role change
      if (users && users.length > 0) {
        const testUser = users.find((u: any) => u.role === 'PATIENT');
        if (testUser) {
          console.log('🔄 Testing role change for user:', testUser.name);
          await api.put(`/admin/users/${testUser.id}/role`, {
            newRole: "DOCTOR",
            reason: "Testing role change functionality"
          });
          console.log('✅ Role change test successful');
        }
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Backend API Connection failed:', error);
    return false;
  }
};
