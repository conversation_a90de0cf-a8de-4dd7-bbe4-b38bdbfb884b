
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Calendar, Clock, User, Search, MapPin } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { useSearchDoctors, useDoctorAvailability, useCreateAppointment } from "@/hooks/usePatient";
import { appointmentSchema, AppointmentFormData } from "@/schemas/validation";
import Header from "@/components/common/Header";

const BookAppointment = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const [selectedSpecialty, setSelectedSpecialty] = useState("");
  const [selectedDoctorId, setSelectedDoctorId] = useState<number | null>(null);
  const [selectedDate, setSelectedDate] = useState("");

  const form = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      patientId: user?.userId || 0,
      doctorId: 0,
      appointmentDate: "",
      reason: "",
      notes: "",
      durationMinutes: 30,
    },
  });

  // Search doctors based on specialty
  const { data: doctors = [], isLoading: doctorsLoading } = useSearchDoctors(selectedSpecialty);

  // Get doctor availability for selected date
  const { data: availableSlots = [], isLoading: slotsLoading } = useDoctorAvailability(
    selectedDoctorId || 0,
    selectedDate
  );

  const createAppointmentMutation = useCreateAppointment();

  const handleSubmit = async (data: AppointmentFormData) => {
    if (!selectedDoctorId) {
      toast({
        title: "Missing Information",
        description: "Please select a doctor.",
        variant: "destructive",
      });
      return;
    }

    try {
      const appointmentData = {
        ...data,
        doctorId: selectedDoctorId,
        patientId: user?.userId || 0,
      };

      await createAppointmentMutation.mutateAsync(appointmentData);

      toast({
        title: "Appointment Booked!",
        description: "Your appointment has been successfully scheduled.",
      });

      navigate('/patient/appointments');
    } catch (error: any) {
      toast({
        title: "Booking Failed",
        description: error.message || "Failed to book appointment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const specialties = [
    { value: "cardiology", label: "Cardiology" },
    { value: "general", label: "General Practice" },
    { value: "dermatology", label: "Dermatology" },
    { value: "orthopedics", label: "Orthopedics" },
    { value: "neurology", label: "Neurology" },
    { value: "pediatrics", label: "Pediatrics" },
    { value: "psychiatry", label: "Psychiatry" },
    { value: "surgery", label: "Surgery" },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="pt-20 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Book Appointment</h1>
            <p className="text-gray-600">Schedule your next medical consultation</p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    New Appointment
                  </CardTitle>
                  <CardDescription>Fill in the details to book your appointment</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Step 1: Select Specialty */}
                  <div className="space-y-2">
                    <Label htmlFor="specialty">Step 1: Select Specialty *</Label>
                    <Select onValueChange={setSelectedSpecialty} required>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a medical specialty" />
                      </SelectTrigger>
                      <SelectContent>
                        {specialties.map((specialty) => (
                          <SelectItem key={specialty.value} value={specialty.value}>
                            {specialty.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Step 2: Select Doctor */}
                  {selectedSpecialty && (
                    <div className="space-y-2">
                      <Label htmlFor="doctor">Step 2: Select Doctor *</Label>
                      {doctorsLoading ? (
                        <div className="p-4 text-center text-gray-500">Loading doctors...</div>
                      ) : doctors.length > 0 ? (
                        <Select onValueChange={(value) => setSelectedDoctorId(Number(value))} required>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose a doctor" />
                          </SelectTrigger>
                          <SelectContent>
                            {doctors.map((doctor) => (
                              <SelectItem key={doctor.id} value={doctor.id.toString()}>
                                <div className="flex flex-col">
                                  <span className="font-medium">{doctor.name}</span>
                                  <span className="text-sm text-gray-500">
                                    {doctor.qualification} • {doctor.yearsOfExperience} years exp • ${doctor.consultationFee}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <div className="p-4 text-center text-gray-500">
                          No doctors available for this specialty
                        </div>
                      )}
                    </div>
                  )}

                  {/* Step 3: Select Date and Time */}
                  {selectedDoctorId && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="appointmentDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Step 3: Select Date *</FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(e.target.value + 'T00:00:00.000Z');
                                  setSelectedDate(e.target.value);
                                }}
                                min={new Date().toISOString().split('T')[0]}
                                required
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {selectedDate && (
                        <div className="space-y-2">
                          <Label>Available Time Slots *</Label>
                          {slotsLoading ? (
                            <div className="p-4 text-center text-gray-500">Loading available slots...</div>
                          ) : availableSlots.length > 0 ? (
                            <div className="grid grid-cols-3 gap-2">
                              {availableSlots.map((slot) => (
                                <Button
                                  key={slot}
                                  type="button"
                                  variant="outline"
                                  className="text-sm"
                                  onClick={() => {
                                    const dateTime = `${selectedDate}T${slot}:00.000Z`;
                                    form.setValue('appointmentDate', dateTime);
                                  }}
                                >
                                  {slot}
                                </Button>
                              ))}
                            </div>
                          ) : (
                            <div className="p-4 text-center text-gray-500">
                              No available slots for this date
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Step 4: Appointment Details */}
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="reason"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Reason for Visit *</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Brief description of your concern or symptoms"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Additional Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Any additional information you'd like to share"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={createAppointmentMutation.isPending || !selectedDoctorId}
                  >
                    {createAppointmentMutation.isPending ? "Booking..." : "Book Appointment"}
                  </Button>
                </CardContent>
              </Card>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default BookAppointment;
