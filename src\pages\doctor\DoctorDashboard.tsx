import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Users, FileText, Clock, Activity, Bell, Phone, Mail, MapPin, Plus } from "lucide-react";
import Header from "@/components/common/Header";

const DoctorDashboard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [todayAppointments, setTodayAppointments] = useState([
    {
      id: 1,
      patient: "<PERSON>",
      time: "09:00 AM",
      duration: "30 min",
      type: "Follow-up",
      status: "confirmed",
      phone: "+1 ************",
      notes: "Blood pressure check"
    },
    {
      id: 2,
      patient: "<PERSON>",
      time: "10:30 AM", 
      duration: "45 min",
      type: "Consultation",
      status: "pending",
      phone: "+1 ************",
      notes: "First visit - general checkup"
    },
    {
      id: 3,
      patient: "Robert <PERSON>",
      time: "02:00 PM",
      duration: "30 min",
      type: "Checkup",
      status: "in-progress",
      phone: "+1 ************",
      notes: "Annual physical examination"
    }
  ]);

  const [upcomingAppointments, setUpcomingAppointments] = useState([
    {
      id: 4,
      patient: "Sarah Davis",
      date: "2024-01-16",
      time: "11:00 AM",
      type: "Consultation",
      status: "confirmed"
    },
    {
      id: 5,
      patient: "Michael Brown",
      date: "2024-01-17",
      time: "03:30 PM",
      type: "Follow-up",
      status: "confirmed"
    }
  ]);

  const [recentPatients, setRecentPatients] = useState([
    {
      id: 1,
      name: "Emma Thompson",
      lastVisit: "2024-01-10",
      condition: "Hypertension",
      status: "stable"
    },
    {
      id: 2,
      name: "James Anderson",
      lastVisit: "2024-01-09",
      condition: "Diabetes Type 2",
      status: "improving"
    },
    {
      id: 3,
      name: "Lisa Wilson",
      lastVisit: "2024-01-08",
      condition: "Asthma",
      status: "stable"
    }
  ]);

  const stats = [
    { title: "Today's Appointments", value: todayAppointments.length, icon: Calendar, color: "text-blue-600" },
    { title: "Total Patients", value: "156", icon: Users, color: "text-green-600" },
    { title: "Pending Reports", value: "3", icon: FileText, color: "text-orange-600" },
    { title: "This Week", value: "24", icon: Clock, color: "text-purple-600" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPatientStatusColor = (status: string) => {
    switch (status) {
      case 'stable': return 'bg-green-100 text-green-800';
      case 'improving': return 'bg-blue-100 text-blue-800';
      case 'attention': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const markAppointmentComplete = (appointmentId: number) => {
    setTodayAppointments(prev => 
      prev.map(apt => 
        apt.id === appointmentId 
          ? { ...apt, status: 'completed' }
          : apt
      )
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="pt-20 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Doctor Dashboard</h1>
                <p className="text-gray-600">Welcome back, Dr. {user?.name}</p>
              </div>
              <div className="flex space-x-3">
                <Button 
                  variant="outline"
                  onClick={() => navigate('/doctor/patients')}
                >
                  <Users className="h-4 w-4 mr-2" />
                  View Patients
                </Button>
                <Button 
                  onClick={() => navigate('/doctor/appointments')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Manage Schedule
                </Button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Today's Schedule */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Today's Schedule
                  </CardTitle>
                  <CardDescription>
                    {new Date().toLocaleDateString('en-US', { 
                      weekday: 'long', 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {todayAppointments.map((appointment) => (
                      <div key={appointment.id} className="border rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <Users className="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                              <h3 className="font-medium">{appointment.patient}</h3>
                              <p className="text-sm text-gray-600">{appointment.type}</p>
                            </div>
                          </div>
                          <Badge className={getStatusColor(appointment.status)}>
                            {appointment.status}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {appointment.time} ({appointment.duration})
                          </div>
                          <div className="flex items-center">
                            <Phone className="h-4 w-4 mr-1" />
                            {appointment.phone}
                          </div>
                        </div>
                        
                        {appointment.notes && (
                          <p className="text-sm text-gray-700 mb-3 p-2 bg-gray-50 rounded">
                            {appointment.notes}
                          </p>
                        )}
                        
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            View Details
                          </Button>
                          {appointment.status !== 'completed' && (
                            <Button 
                              size="sm"
                              onClick={() => markAppointmentComplete(appointment.id)}
                            >
                              Mark Complete
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="h-5 w-5 mr-2" />
                    Practice Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">8</div>
                      <div className="text-sm text-blue-600">Patients Today</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">24</div>
                      <div className="text-sm text-green-600">This Week</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">156</div>
                      <div className="text-sm text-purple-600">Total Patients</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Upcoming Appointments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center">
                      <Clock className="h-5 w-5 mr-2" />
                      Upcoming
                    </span>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => navigate('/doctor/appointments')}
                    >
                      View All
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {upcomingAppointments.map((appointment) => (
                      <div key={appointment.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium text-sm">{appointment.patient}</h4>
                          <Badge variant="outline" className="text-xs">
                            {appointment.status}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-600 mb-1">{appointment.type}</p>
                        <div className="flex items-center text-xs text-gray-600">
                          <Calendar className="h-3 w-3 mr-1" />
                          {new Date(appointment.date).toLocaleDateString()} at {appointment.time}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Patients */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      Recent Patients
                    </span>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => navigate('/doctor/patients')}
                    >
                      View All
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentPatients.map((patient) => (
                      <div key={patient.id} className="p-3 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium text-sm">{patient.name}</h4>
                          <Badge className={getPatientStatusColor(patient.status)}>
                            {patient.status}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-600 mb-1">{patient.condition}</p>
                        <p className="text-xs text-gray-500">
                          Last visit: {new Date(patient.lastVisit).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Plus className="h-5 w-5 mr-2" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => navigate('/doctor/diagnosis/new')}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      New Diagnosis
                    </Button>
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => navigate('/doctor/patients')}
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Patient Records
                    </Button>
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => navigate('/doctor/profile')}
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Update Profile
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoctorDashboard;
