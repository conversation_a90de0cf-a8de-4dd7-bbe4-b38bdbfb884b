
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import { NotificationProvider } from "./context/NotificationContext";

// Pages
import Index from "./pages/Index";
import Login from "./pages/auth/Login";
import Register from "./pages/auth/Register";
import NotFound from "./pages/NotFound";

// Protected Routes
import ProtectedRoute from "./components/layout/ProtectedRoute";
import RoleBasedRoute from "./components/layout/RoleBasedRoute";

// Dashboards
import PatientDashboard from "./pages/patient/PatientDashboard";
import DoctorDashboard from "./pages/doctor/DoctorDashboard";
import ClinicDashboard from "./pages/clinic/ClinicDashboard";
import AdminDashboard from "./pages/admin/AdminDashboard";

// Patient Pages
import BookAppointment from "./pages/patient/BookAppointment";
import AppointmentHistory from "./pages/patient/AppointmentHistory";
import DoctorSearch from "./pages/patient/DoctorSearch";
import PatientProfile from "./pages/patient/PatientProfile";
import MedicalHistory from "./pages/patient/MedicalHistory";

// Doctor Pages
import DoctorProfile from "./pages/doctor/DoctorProfile";
import PatientList from "./pages/doctor/PatientList";
import AppointmentManagement from "./pages/doctor/AppointmentManagement";
import DiagnosisEntry from "./pages/doctor/DiagnosisEntry";

// Clinic Pages
import ClinicProfile from "./pages/clinic/ClinicProfile";
import StaffManagement from "./pages/clinic/StaffManagement";
import ClinicAppointments from "./pages/clinic/ClinicAppointments";

// Admin Pages
import UserManagement from "./pages/admin/UserManagement";
import DoctorApproval from "./pages/admin/DoctorApproval";
import SystemReports from "./pages/admin/SystemReports";
import AdminSettings from "./pages/admin/AdminSettings";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <NotificationProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* Patient Routes */}
              <Route path="/patient" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['PATIENT']}>
                    <PatientDashboard />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/patient/book-appointment" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['PATIENT']}>
                    <BookAppointment />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/patient/appointments" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['PATIENT']}>
                    <AppointmentHistory />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/patient/doctors" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['PATIENT']}>
                    <DoctorSearch />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/patient/profile" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['PATIENT']}>
                    <PatientProfile />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/patient/medical-history" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['PATIENT']}>
                    <MedicalHistory />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />

              {/* Doctor Routes */}
              <Route path="/doctor" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['DOCTOR']}>
                    <DoctorDashboard />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/doctor/profile" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['DOCTOR']}>
                    <DoctorProfile />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/doctor/patients" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['DOCTOR']}>
                    <PatientList />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/doctor/appointments" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['DOCTOR']}>
                    <AppointmentManagement />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/doctor/diagnosis/:patientId" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['DOCTOR']}>
                    <DiagnosisEntry />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />

              {/* Clinic Routes */}
              <Route path="/clinic" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['CLINIC']}>
                    <ClinicDashboard />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/clinic/profile" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['CLINIC']}>
                    <ClinicProfile />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/clinic/staff" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['CLINIC']}>
                    <StaffManagement />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/clinic/appointments" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['CLINIC']}>
                    <ClinicAppointments />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />

              {/* Admin Routes */}
              <Route path="/admin" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['ADMIN']}>
                    <AdminDashboard />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/admin/users" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['ADMIN']}>
                    <UserManagement />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/admin/doctors" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['ADMIN']}>
                    <DoctorApproval />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/admin/reports" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['ADMIN']}>
                    <SystemReports />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />
              <Route path="/admin/settings" element={
                <ProtectedRoute>
                  <RoleBasedRoute allowedRoles={['ADMIN']}>
                    <AdminSettings />
                  </RoleBasedRoute>
                </ProtectedRoute>
              } />

              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </NotificationProvider>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
