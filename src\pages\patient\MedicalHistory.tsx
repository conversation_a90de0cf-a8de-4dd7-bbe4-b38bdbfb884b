
import { Card, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Pill, Activity, Calendar, Download, Eye } from "lucide-react";

const MedicalHistory = () => {
  const diagnoses = [
    {
      id: 1,
      date: "2024-01-15",
      doctor: "Dr. <PERSON>",
      diagnosis: "Hypertension",
      notes: "Blood pressure monitoring required. Patient advised to reduce sodium intake and increase physical activity.",
      severity: "Moderate"
    },
    {
      id: 2,
      date: "2023-12-10",
      doctor: "Dr. <PERSON>",
      diagnosis: "Seasonal Allergies",
      notes: "Allergic reaction to pollen. Prescribed antihistamines for symptom management.",
      severity: "Mild"
    },
    {
      id: 3,
      date: "2023-08-22",
      doctor: "Dr. <PERSON>",
      diagnosis: "Vitamin D Deficiency",
      notes: "Low vitamin D levels detected. Recommended supplements and increased sun exposure.",
      severity: "Mild"
    }
  ];

  const prescriptions = [
    {
      id: 1,
      date: "2024-01-15",
      medication: "Lisinopril 10mg",
      dosage: "Once daily",
      duration: "30 days",
      doctor: "Dr. <PERSON>",
      status: "Active"
    },
    {
      id: 2,
      date: "2023-12-10",
      medication: "Cetirizine 10mg",
      dosage: "Once daily as needed",
      duration: "90 days",
      doctor: "Dr. <PERSON>",
      status: "Completed"
    },
    {
      id: 3,
      date: "2023-08-22",
      medication: "Vitamin D3 2000 IU",
      dosage: "Once daily",
      duration: "60 days",
      doctor: "Dr. Emily Williams",
      status: "Completed"
    }
  ];

  const visits = [
    {
      id: 1,
      date: "2024-01-15",
      doctor: "Dr. Sarah Smith",
      type: "Regular Checkup",
      notes: "Annual physical examination. Blood pressure elevated, recommended lifestyle changes.",
      duration: "45 minutes"
    },
    {
      id: 2,
      date: "2023-12-10",
      doctor: "Dr. Michael Johnson",
      type: "Consultation",
      notes: "Patient complained of sneezing and watery eyes. Diagnosed with seasonal allergies.",
      duration: "30 minutes"
    },
    {
      id: 3,
      date: "2023-08-22",
      doctor: "Dr. Emily Williams",
      type: "Follow-up",
      notes: "Blood test results review. Vitamin D deficiency confirmed and treatment plan discussed.",
      duration: "20 minutes"
    }
  ];

  const testResults = [
    {
      id: 1,
      date: "2024-01-15",
      test: "Complete Blood Count",
      result: "Normal",
      doctor: "Dr. Sarah Smith",
      status: "Available"
    },
    {
      id: 2,
      date: "2024-01-15",
      test: "Lipid Panel",
      result: "Cholesterol slightly elevated",
      doctor: "Dr. Sarah Smith",
      status: "Available"
    },
    {
      id: 3,
      date: "2023-08-20",
      test: "Vitamin D Level",
      result: "Deficient (18 ng/mL)",
      doctor: "Dr. Emily Williams",
      status: "Available"
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Mild': return 'bg-green-100 text-green-800';
      case 'Moderate': return 'bg-yellow-100 text-yellow-800';
      case 'Severe': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-blue-100 text-blue-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Available': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Medical History</h1>
          <p className="text-gray-600">Complete overview of your medical records</p>
        </div>

        <Tabs defaultValue="diagnoses" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="diagnoses">Diagnoses</TabsTrigger>
            <TabsTrigger value="prescriptions">Prescriptions</TabsTrigger>
            <TabsTrigger value="visits">Visits</TabsTrigger>
            <TabsTrigger value="tests">Test Results</TabsTrigger>
          </TabsList>

          <TabsContent value="diagnoses" className="space-y-4">
            {diagnoses.map((diagnosis) => (
              <Card key={diagnosis.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{diagnosis.diagnosis}</CardTitle>
                    <div className="flex gap-2">
                      <Badge className={getSeverityColor(diagnosis.severity)}>
                        {diagnosis.severity}
                      </Badge>
                      <Badge variant="outline">{diagnosis.date}</Badge>
                    </div>
                  </div>
                  <CardDescription>Diagnosed by {diagnosis.doctor}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{diagnosis.notes}</p>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="prescriptions" className="space-y-4">
            {prescriptions.map((prescription) => (
              <Card key={prescription.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{prescription.medication}</CardTitle>
                    <div className="flex gap-2">
                      <Badge className={getStatusColor(prescription.status)}>
                        {prescription.status}
                      </Badge>
                      <Badge variant="outline">{prescription.date}</Badge>
                    </div>
                  </div>
                  <CardDescription>
                    {prescription.dosage} for {prescription.duration} - Prescribed by {prescription.doctor}
                  </CardDescription>
                </CardHeader>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="visits" className="space-y-4">
            {visits.map((visit) => (
              <Card key={visit.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{visit.type}</CardTitle>
                    <div className="flex gap-2">
                      <Badge variant="secondary">{visit.duration}</Badge>
                      <Badge variant="outline">{visit.date}</Badge>
                    </div>
                  </div>
                  <CardDescription>with {visit.doctor}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{visit.notes}</p>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="tests" className="space-y-4">
            {testResults.map((test) => (
              <Card key={test.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{test.test}</CardTitle>
                    <div className="flex gap-2">
                      <Badge className={getStatusColor(test.status)}>
                        {test.status}
                      </Badge>
                      <Badge variant="outline">{test.date}</Badge>
                    </div>
                  </div>
                  <CardDescription>Ordered by {test.doctor}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Result: {test.result}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MedicalHistory;
