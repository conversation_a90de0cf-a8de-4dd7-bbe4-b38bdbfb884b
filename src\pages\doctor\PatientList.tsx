
import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Search, Eye, Calendar, Phone, Mail, AlertCircle, Filter, X, ChevronLeft, ChevronRight } from "lucide-react";
import Header from "@/components/common/Header";
import { useAuth } from "@/context/AuthContext";
import { useDoctorPatientsData, useScheduleAppointment } from "@/hooks/useDoctorPatients";
import { useToast } from "@/hooks/use-toast";

const PatientList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();

  // Local state
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<{ id: number; name: string } | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Get doctor ID from user context
  const doctorId = user?.id || 1; // Fallback for development

  // Fetch patients data from backend
  const {
    patients,
    stats,
    isLoading,
    isError,
    error
  } = useDoctorPatientsData(doctorId);

  // Schedule appointment mutation
  const scheduleAppointmentMutation = useScheduleAppointment();

  // Filter patients based on search and status
  const filteredPatients = useMemo(() => {
    if (!patients) return [];

    return patients.filter(patient => {
      const matchesSearch = searchQuery === "" ||
        patient.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        patient.condition.toLowerCase().includes(searchQuery.toLowerCase()) ||
        patient.email.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus = statusFilter === "all" || patient.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [patients, searchQuery, statusFilter]);

  // Handle patient view
  const handleViewPatient = (patientId: number) => {
    navigate(`/doctor/diagnosis/${patientId}`);
  };

  // Handle schedule appointment - open calendar modal
  const handleScheduleAppointment = (patientId: number, patientName: string) => {
    setSelectedPatient({ id: patientId, name: patientName });
    setIsCalendarOpen(true);
    setSelectedDate(null);
    setCurrentMonth(new Date());
  };

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  // Handle calendar navigation
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      if (direction === 'prev') {
        newMonth.setMonth(prev.getMonth() - 1);
      } else {
        newMonth.setMonth(prev.getMonth() + 1);
      }
      return newMonth;
    });
  };

  // Determine which tab to navigate to based on selected date
  const getAppointmentTab = (selectedDate: Date) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    // Reset time to compare only dates
    today.setHours(0, 0, 0, 0);
    tomorrow.setHours(0, 0, 0, 0);
    const compareDate = new Date(selectedDate);
    compareDate.setHours(0, 0, 0, 0);

    if (compareDate.getTime() === today.getTime()) {
      return 'today';
    } else if (compareDate.getTime() === tomorrow.getTime()) {
      return 'upcoming';
    } else if (compareDate > tomorrow) {
      return 'upcoming';
    } else {
      return 'completed';
    }
  };

  // Handle confirm appointment
  const handleConfirmAppointment = async () => {
    if (selectedDate && selectedPatient) {
      const targetTab = getAppointmentTab(selectedDate);

      // Create appointment data for backend
      const appointmentData = {
        doctorId: doctorId,
        patientId: selectedPatient.id,
        date: selectedDate.toISOString().split('T')[0], // YYYY-MM-DD format
        time: "09:00", // Default time - could be made configurable
        duration: 30, // Default 30 minutes
        type: "consultation" as const, // Must match the type union
        status: "scheduled" as const, // Must match the status union
        notes: `Appointment scheduled for ${selectedPatient.name}`
      };

      try {
        // Save appointment to backend
        await scheduleAppointmentMutation.mutateAsync(appointmentData);

        setIsCalendarOpen(false);

        // Navigate to appointments page with the scheduled appointment info
        navigate('/doctor/appointments', {
          state: {
            selectedPatientId: selectedPatient.id,
            selectedDate: selectedDate.toISOString(),
            patientName: selectedPatient.name,
            targetTab: targetTab,
            appointmentCreated: true
          }
        });

      } catch (error) {
        console.error('Failed to schedule appointment:', error);
        // Error handling is already done by the mutation hook via toast
      }
    }
  };

  // Close calendar modal
  const handleCloseCalendar = () => {
    setIsCalendarOpen(false);
    setSelectedPatient(null);
    setSelectedDate(null);
  };

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-yellow-100 text-yellow-800';
      case 'discharged': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  // Calendar helper functions
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSameDay = (date1: Date, date2: Date) => {
    return date1.toDateString() === date2.toDateString();
  };

  const isPastDate = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  // Calendar component
  const CalendarModal = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const firstDay = getFirstDayOfMonth(currentMonth);
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];
    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-10"></div>);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      const isSelected = selectedDate && isSameDay(date, selectedDate);
      const isPast = isPastDate(date);
      const isTodayDate = isToday(date);

      days.push(
        <button
          key={day}
          onClick={() => !isPast && handleDateSelect(date)}
          disabled={isPast}
          className={`
            h-10 w-10 rounded-lg text-sm font-medium transition-colors
            ${isPast
              ? 'text-gray-300 cursor-not-allowed'
              : 'hover:bg-blue-100 cursor-pointer'
            }
            ${isSelected
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : ''
            }
            ${isTodayDate && !isSelected
              ? 'bg-blue-50 text-blue-600 border border-blue-200'
              : ''
            }
          `}
        >
          {day}
        </button>
      );
    }

    return (
      <Dialog open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Schedule Appointment</DialogTitle>
            <DialogDescription>
              Select a date for {selectedPatient?.name}'s appointment
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Calendar Header */}
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('prev')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h3 className="text-lg font-semibold">
                {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('next')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Day Names */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {dayNames.map(day => (
                <div key={day} className="h-10 flex items-center justify-center text-sm font-medium text-gray-500">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1">
              {days}
            </div>

            {/* Selected Date Display */}
            {selectedDate && (
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Selected Date:</strong> {selectedDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={handleCloseCalendar}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button
                onClick={handleConfirmAppointment}
                disabled={!selectedDate}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Confirm Appointment
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, index) => (
        <Card key={index} className="animate-pulse">
          <CardHeader>
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-6 w-16" />
            </div>
            <Skeleton className="h-4 w-24" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
            <div className="flex space-x-2">
              <Skeleton className="h-8 flex-1" />
              <Skeleton className="h-8 flex-1" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="pt-20 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Patient List</h1>
                <p className="text-gray-600">Manage and view your assigned patients</p>
              </div>

            </div>
          </div>

          {/* Patient Statistics */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-blue-600">{stats.totalPatients}</div>
                  <div className="text-sm text-gray-600">Total Patients</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">{stats.activePatients}</div>
                  <div className="text-sm text-gray-600">Active Patients</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-purple-600">{stats.newPatientsThisMonth}</div>
                  <div className="text-sm text-gray-600">New This Month</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-orange-600">{stats.upcomingAppointments}</div>
                  <div className="text-sm text-gray-600">Upcoming Appointments</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-indigo-600">{stats.completedAppointmentsToday}</div>
                  <div className="text-sm text-gray-600">Completed Today</div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Error Alert */}
          {isError && (
            <Alert className="mb-6 border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <div className="font-medium">Failed to load patient data</div>
                <div className="text-sm mt-1">
                  {error?.message || 'Unable to fetch data from the backend. Using fallback data where available.'}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Search and Filter Controls */}
          <div className="mb-6 space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search patients by name, condition, or email..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <Tabs value={statusFilter} onValueChange={setStatusFilter} className="w-auto">
                  <TabsList>
                    <TabsTrigger value="all">All</TabsTrigger>
                    <TabsTrigger value="active">Active</TabsTrigger>
                    <TabsTrigger value="inactive">Inactive</TabsTrigger>
                    <TabsTrigger value="discharged">Discharged</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </div>
          </div>

          {/* Patient Cards */}
          {isLoading ? (
            <LoadingSkeleton />
          ) : (
            <>
              {filteredPatients.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-500 text-lg mb-2">
                    {searchQuery || statusFilter !== "all"
                      ? "No patients found matching your criteria"
                      : "No patients assigned yet"
                    }
                  </div>
                  <div className="text-gray-400 text-sm">
                    {searchQuery || statusFilter !== "all"
                      ? "Try adjusting your search or filter settings"
                      : "Patients will appear here once they are assigned to you"
                    }
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredPatients.map((patient) => (
                    <Card key={patient.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{patient.name}</CardTitle>
                          <Badge className={getStatusColor(patient.status)}>
                            {patient.status}
                          </Badge>
                        </div>
                        <CardDescription>
                          {patient.age} years old • {patient.gender}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            <span><strong>Last Visit:</strong> {new Date(patient.lastVisit).toLocaleDateString()}</span>
                          </div>
                          {patient.nextAppointment && (
                            <div className="flex items-center text-sm text-blue-600">
                              <Calendar className="h-4 w-4 mr-2" />
                              <span><strong>Next:</strong> {new Date(patient.nextAppointment).toLocaleDateString()}</span>
                            </div>
                          )}
                          <div className="flex items-center text-sm text-gray-600">
                            <AlertCircle className="h-4 w-4 mr-2" />
                            <span><strong>Condition:</strong> {patient.condition}</span>
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <Phone className="h-4 w-4 mr-2" />
                            <span>{patient.phone}</span>
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <Mail className="h-4 w-4 mr-2" />
                            <span>{patient.email}</span>
                          </div>
                          {patient.allergies && (
                            <div className="flex items-start text-sm text-red-600">
                              <AlertCircle className="h-4 w-4 mr-2 mt-0.5" />
                              <span><strong>Allergies:</strong> {
                                Array.isArray(patient.allergies)
                                  ? patient.allergies.join(', ')
                                  : typeof patient.allergies === 'string'
                                    ? patient.allergies
                                    : 'Not specified'
                              }</span>
                            </div>
                          )}
                        </div>

                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1"
                            onClick={() => handleViewPatient(patient.id)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                          <Button
                            size="sm"
                            className="flex-1"
                            onClick={() => handleScheduleAppointment(patient.id, patient.name)}
                            disabled={scheduleAppointmentMutation.isPending}
                          >
                            <Calendar className="h-4 w-4 mr-1" />
                            Schedule
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Calendar Modal */}
      <CalendarModal />
    </div>
  );
};

export default PatientList;
