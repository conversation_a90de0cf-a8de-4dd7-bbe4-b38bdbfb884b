
import { <PERSON>, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, Eye, Calendar } from "lucide-react";

const PatientList = () => {
  const patients = [
    {
      id: 1,
      name: "<PERSON>",
      age: 45,
      gender: "Male",
      lastVisit: "2024-01-15",
      condition: "Hypertension",
      status: "active"
    },
    {
      id: 2,
      name: "<PERSON>",
      age: 32,
      gender: "Female",
      lastVisit: "2024-01-10",
      condition: "Diabetes",
      status: "active"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Patient List</h1>
          <p className="text-gray-600">Manage and view your assigned patients</p>
        </div>

        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search patients by name or condition..."
              className="pl-10"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {patients.map((patient) => (
            <Card key={patient.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{patient.name}</CardTitle>
                  <Badge className="bg-green-100 text-green-800">
                    {patient.status}
                  </Badge>
                </div>
                <CardDescription>
                  {patient.age} years old • {patient.gender}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    <strong>Last Visit:</strong> {patient.lastVisit}
                  </p>
                  <p className="text-sm text-gray-600">
                    <strong>Condition:</strong> {patient.condition}
                  </p>
                </div>
                
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button size="sm" className="flex-1">
                    <Calendar className="h-4 w-4 mr-1" />
                    Schedule
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PatientList;
