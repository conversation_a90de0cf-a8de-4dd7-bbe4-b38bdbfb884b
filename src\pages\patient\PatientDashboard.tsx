
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Calendar, Clock, User, Heart, Activity, FileText, Search, Plus, Bell, MapPin, Phone } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { usePatientAppointments, usePatientDashboardStats } from "@/hooks/usePatient";
import Header from "@/components/common/Header";

const PatientDashboard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Fetch real data using React Query hooks
  const { data: appointments = [], isLoading: appointmentsLoading } = usePatientAppointments(
    user?.userId || 0,
    'SCHEDULED,CONFIRMED'
  );

  const { data: dashboardStats, isLoading: statsLoading } = usePatientDashboardStats(
    user?.userId || 0
  );

  // Filter upcoming appointments (next 7 days)
  const upcomingAppointments = appointments.filter(appointment => {
    const appointmentDate = new Date(appointment.appointmentDate);
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    return appointmentDate >= today && appointmentDate <= nextWeek;
  }).slice(0, 3); // Show only first 3

  // Mock recent activity - this would come from API in real implementation
  const recentActivity = [
    {
      id: 1,
      type: "appointment",
      description: "Appointment scheduled with Dr. Sarah Johnson",
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      icon: Calendar
    },
    {
      id: 2,
      type: "prescription",
      description: "New prescription added for blood pressure medication",
      date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
      icon: FileText
    },
    {
      id: 3,
      type: "test_result",
      description: "Blood test results available",
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      icon: Activity
    }
  ];

  // Mock health metrics - this would come from API
  const healthMetrics = {
    bloodPressure: "120/80",
    heartRate: "72 bpm",
    weight: "70 kg",
    lastCheckup: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
  };

  const stats = [
    {
      title: "Upcoming Appointments",
      value: upcomingAppointments.length,
      icon: Calendar,
      color: "text-blue-600",
      bg: "bg-blue-50"
    },
    {
      title: "Total Appointments",
      value: dashboardStats?.totalAppointments ?? appointments.length,
      icon: Clock,
      color: "text-green-600",
      bg: "bg-green-50"
    },
    {
      title: "Active Prescriptions",
      value: dashboardStats?.activePrescriptions ?? "3",
      icon: FileText,
      color: "text-purple-600",
      bg: "bg-purple-50"
    },
    {
      title: "Health Score",
      value: dashboardStats?.healthScore ?? "95%",
      icon: Activity,
      color: "text-orange-600",
      bg: "bg-orange-50"
    }
  ];

  const quickActions = [
    {
      title: "Book Appointment",
      description: "Schedule a visit with your doctor",
      icon: Calendar,
      action: () => navigate('/patient/book-appointment'),
      color: "bg-blue-600 hover:bg-blue-700"
    },
    {
      title: "Find Doctors",
      description: "Search for specialists",
      icon: Search,
      action: () => navigate('/patient/doctors'),
      color: "bg-green-600 hover:bg-green-700"
    },
    {
      title: "Medical History",
      description: "View your medical records",
      icon: FileText,
      action: () => navigate('/patient/medical-history'),
      color: "bg-purple-600 hover:bg-purple-700"
    },
    {
      title: "Update Profile",
      description: "Manage your information",
      icon: User,
      action: () => navigate('/patient/profile'),
      color: "bg-orange-600 hover:bg-orange-700"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="pt-20 p-6">
        <div className="max-w-6xl mx-auto">
          {/* Welcome Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center mb-2">
                  <Heart className="h-8 w-8 text-blue-600 mr-3" />
                  <h1 className="text-3xl font-bold text-gray-900">
                    Welcome back, {user?.name}!
                  </h1>
                </div>
                <p className="text-gray-600">
                  Here's an overview of your healthcare journey with MediConnect
                </p>
              </div>
              <Button onClick={() => navigate('/patient/book-appointment')} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                Book Appointment
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      {statsLoading ? (
                        <Skeleton className="h-8 w-16 mt-1" />
                      ) : (
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      )}
                    </div>
                    <div className={`p-3 rounded-lg ${stat.bg}`}>
                      <stat.icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Quick Actions */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Plus className="h-5 w-5 mr-2" />
                    Quick Actions
                  </CardTitle>
                  <CardDescription>
                    Manage your healthcare needs efficiently
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {quickActions.map((action, index) => (
                      <div
                        key={index}
                        onClick={action.action}
                        className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 cursor-pointer transition-all group"
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`p-2 rounded-lg ${action.color} transition-colors`}>
                            <action.icon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900 group-hover:text-blue-600">
                              {action.title}
                            </h3>
                            <p className="text-sm text-gray-600">{action.description}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Health Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="h-5 w-5 mr-2" />
                    Health Metrics
                  </CardTitle>
                  <CardDescription>Your latest health indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-600 font-medium">Blood Pressure</p>
                      <p className="text-xl font-bold text-blue-900">{healthMetrics.bloodPressure}</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <p className="text-sm text-green-600 font-medium">Heart Rate</p>
                      <p className="text-xl font-bold text-green-900">{healthMetrics.heartRate}</p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <p className="text-sm text-purple-600 font-medium">Weight</p>
                      <p className="text-xl font-bold text-purple-900">{healthMetrics.weight}</p>
                    </div>
                    <div className="p-4 bg-orange-50 rounded-lg">
                      <p className="text-sm text-orange-600 font-medium">Last Checkup</p>
                      <p className="text-xl font-bold text-orange-900">{new Date(healthMetrics.lastCheckup).toLocaleDateString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar Content */}
            <div className="space-y-6">
              {/* Upcoming Appointments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center">
                      <Calendar className="h-5 w-5 mr-2" />
                      Upcoming Appointments
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate('/patient/appointments')}
                    >
                      View All
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {appointmentsLoading ? (
                    <div className="space-y-3">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <Skeleton className="h-4 w-32" />
                            <Skeleton className="h-5 w-16" />
                          </div>
                          <Skeleton className="h-3 w-24 mb-2" />
                          <Skeleton className="h-3 w-40 mb-1" />
                          <Skeleton className="h-3 w-36" />
                        </div>
                      ))}
                    </div>
                  ) : upcomingAppointments.length > 0 ? (
                    <div className="space-y-3">
                      {upcomingAppointments.map((appointment) => (
                        <div key={appointment.id} className="p-3 border rounded-lg hover:bg-gray-50">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">{appointment.doctorName}</h4>
                            <Badge className={getStatusColor(appointment.status)}>
                              {appointment.status}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-600 mb-2">{appointment.doctorSpecialty}</p>
                          <div className="flex items-center text-xs text-gray-600 mb-1">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(appointment.appointmentDate).toLocaleDateString()} at{' '}
                            {new Date(appointment.appointmentDate).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                          <div className="flex items-center text-xs text-gray-600">
                            <MapPin className="h-3 w-3 mr-1" />
                            {appointment.clinicName}
                          </div>
                          <div className="mt-2 text-xs text-gray-600">
                            <strong>Reason:</strong> {appointment.reason}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-600 text-sm mb-3">
                        No upcoming appointments
                      </p>
                      <Button
                        onClick={() => navigate('/patient/book-appointment')}
                        size="sm"
                      >
                        Book Your First Appointment
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Bell className="h-5 w-5 mr-2" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentActivity.map((activity) => (
                      <div key={activity.id} className="flex items-start space-x-3 p-2 hover:bg-gray-50 rounded">
                        <div className="p-1 bg-blue-100 rounded">
                          <activity.icon className="h-3 w-3 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-900">{activity.description}</p>
                          <p className="text-xs text-gray-500">{new Date(activity.date).toLocaleDateString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientDashboard;
