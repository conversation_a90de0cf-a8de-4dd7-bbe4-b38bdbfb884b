
import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Calendar, Clock, User, CheckCircle, Info, X } from "lucide-react";
import Header from "@/components/common/Header";

const AppointmentManagement = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("today");
  const [schedulingInfo, setSchedulingInfo] = useState<{
    patientName?: string;
    selectedDate?: string;
    targetTab?: string;
  } | null>(null);

  // Handle navigation from patient scheduling
  useEffect(() => {
    if (location.state) {
      const { selectedPatientId, selectedDate, patientName, targetTab } = location.state;

      if (targetTab && patientName && selectedDate) {
        setActiveTab(targetTab);
        setSchedulingInfo({
          patientName,
          selectedDate,
          targetTab
        });

        // Clear the location state after processing
        window.history.replaceState({}, document.title);
      }
    }
  }, [location.state]);

  const upcomingAppointments = [
    {
      id: 1,
      patient: "John Smith",
      time: "09:00 AM",
      duration: "30 min",
      type: "Follow-up",
      status: "confirmed"
    },
    {
      id: 2,
      patient: "Mary Johnson",
      time: "10:30 AM", 
      duration: "45 min",
      type: "Consultation",
      status: "pending"
    }
  ];

  const todayAppointments = [
    {
      id: 3,
      patient: "Robert Wilson",
      time: "02:00 PM",
      duration: "30 min",
      type: "Checkup",
      status: "completed"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function to format date for display
  const formatDateForDisplay = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Helper function to get tab display name
  const getTabDisplayName = (tab: string) => {
    switch (tab) {
      case 'today': return 'Today';
      case 'upcoming': return 'Upcoming';
      case 'completed': return 'Completed';
      default: return tab;
    }
  };

  // Dismiss scheduling info alert
  const dismissSchedulingInfo = () => {
    setSchedulingInfo(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="pt-20 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Appointment Management</h1>
            <p className="text-gray-600">Manage your daily schedule and patient appointments</p>
          </div>

          {/* Scheduling Info Alert */}
          {schedulingInfo && (
            <Alert className="mb-6 border-blue-200 bg-blue-50 relative">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800 pr-8">
                <div className="font-medium">Appointment Scheduled Successfully</div>
                <div className="text-sm mt-1">
                  Appointment with <strong>{schedulingInfo.patientName}</strong> scheduled for{' '}
                  <strong>{formatDateForDisplay(schedulingInfo.selectedDate!)}</strong> has been added to the{' '}
                  <strong>{getTabDisplayName(schedulingInfo.targetTab!)}</strong> tab.
                </div>
              </AlertDescription>
              <Button
                variant="ghost"
                size="sm"
                onClick={dismissSchedulingInfo}
                className="absolute top-2 right-2 h-6 w-6 p-0 text-blue-600 hover:text-blue-800 hover:bg-blue-100"
              >
                <X className="h-4 w-4" />
              </Button>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="today">Today</TabsTrigger>
              <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
            </TabsList>

            <TabsContent value="today" className="space-y-4">
              {todayAppointments.map((appointment) => (
                <Card key={appointment.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{appointment.patient}</CardTitle>
                      <Badge className={getStatusColor(appointment.status)}>
                        {appointment.status}
                      </Badge>
                    </div>
                    <CardDescription>{appointment.type}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1 text-gray-500" />
                          <span>{appointment.time}</span>
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                          <span>{appointment.duration}</span>
                        </div>
                      </div>
                      <div className="space-x-2">
                        <Button size="sm" variant="outline">View Details</Button>
                        <Button size="sm">
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Complete
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="upcoming" className="space-y-4">
              {upcomingAppointments.map((appointment) => (
                <Card key={appointment.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{appointment.patient}</CardTitle>
                      <Badge className={getStatusColor(appointment.status)}>
                        {appointment.status}
                      </Badge>
                    </div>
                    <CardDescription>{appointment.type}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1 text-gray-500" />
                          <span>{appointment.time}</span>
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                          <span>{appointment.duration}</span>
                        </div>
                      </div>
                      <div className="space-x-2">
                        <Button size="sm" variant="outline">Reschedule</Button>
                        <Button size="sm">Confirm</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="completed" className="space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <p className="text-gray-600 text-center">No completed appointments today</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default AppointmentManagement;
