
import { useState, useEffect, useMemo } from "react";
import { useLocation } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, Clock, User, CheckCircle, Info, X, AlertCircle, Eye, CalendarDays, FileText, Chevron<PERSON>eft, ChevronRight } from "lucide-react";
import Header from "@/components/common/Header";
import { useAuth } from "@/context/AuthContext";
import { useDoctorTodayAppointments, useDoctorUpcomingAppointments } from "@/hooks/useDoctor";
import { useToast } from "@/hooks/use-toast";

interface AppointmentData {
  id: number;
  patientId: number;
  patientName: string;
  doctorId: number;
  appointmentDate: string;
  status: string;
  reason: string;
  notes?: string;
  durationMinutes: number;
  createdAt: string;
  isLocal?: boolean;
}

const AppointmentManagement = () => {
  const location = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("today");
  const [schedulingInfo, setSchedulingInfo] = useState<{
    patientName?: string;
    selectedDate?: string;
    targetTab?: string;
  } | null>(null);

  // Local state for frontend-scheduled appointments (fallback when backend fails)
  const [localAppointments, setLocalAppointments] = useState<AppointmentData[]>([]);

  // Modal states
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentData | null>(null);
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false);
  const [isRescheduleOpen, setIsRescheduleOpen] = useState(false);
  const [isViewNotesOpen, setIsViewNotesOpen] = useState(false);

  // Reschedule form state
  const [rescheduleDate, setRescheduleDate] = useState("");
  const [rescheduleTime, setRescheduleTime] = useState("");
  const [rescheduleNotes, setRescheduleNotes] = useState("");

  // Calendar state for reschedule
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Get doctor ID from user context
  const doctorId = user?.id || 1; // Fallback for development

  // Fetch real appointment data from backend
  const {
    data: todayAppointments = [],
    isLoading: todayLoading,
    isError: todayError,
    error: todayErrorDetails
  } = useDoctorTodayAppointments(doctorId);

  const {
    data: upcomingAppointments = [],
    isLoading: upcomingLoading,
    isError: upcomingError,
    error: upcomingErrorDetails
  } = useDoctorUpcomingAppointments(doctorId);

  // Handle navigation from patient scheduling
  useEffect(() => {
    if (location.state) {
      const { selectedPatientId, selectedDate, patientName, targetTab, appointmentCreated } = location.state;

      if (targetTab && patientName && selectedDate) {
        setActiveTab(targetTab);
        setSchedulingInfo({
          patientName,
          selectedDate,
          targetTab
        });

        // If appointment was created, add it to local state for immediate display
        if (appointmentCreated) {
          const newAppointment = {
            id: Date.now(), // Temporary ID
            patientId: selectedPatientId,
            patientName: patientName,
            doctorId: doctorId,
            appointmentDate: selectedDate,
            status: 'SCHEDULED',
            reason: 'Consultation',
            notes: `Appointment scheduled for ${patientName}`,
            durationMinutes: 30,
            createdAt: new Date().toISOString(),
            isLocal: true // Flag to identify frontend-created appointments
          };

          setLocalAppointments(prev => [...prev, newAppointment]);
        }

        // Clear the location state after processing
        window.history.replaceState({}, document.title);
      }
    }
  }, [location.state, doctorId]);

  // Merge backend appointments with local appointments and filter by date
  const mergedTodayAppointments = useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    const localTodayAppointments = localAppointments.filter(appointment =>
      appointment.appointmentDate.split('T')[0] === today
    );
    return [...todayAppointments, ...localTodayAppointments];
  }, [todayAppointments, localAppointments]);

  const mergedUpcomingAppointments = useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    const localUpcomingAppointments = localAppointments.filter(appointment =>
      appointment.appointmentDate.split('T')[0] > today
    );
    return [...upcomingAppointments, ...localUpcomingAppointments];
  }, [upcomingAppointments, localAppointments]);

  // Filter completed appointments
  const completedAppointments = useMemo(() => {
    const completedFromToday = mergedTodayAppointments.filter(appointment =>
      appointment.status === 'COMPLETED'
    );
    const localCompletedAppointments = localAppointments.filter(appointment =>
      appointment.status === 'COMPLETED'
    );
    return [...completedFromToday, ...localCompletedAppointments];
  }, [mergedTodayAppointments, localAppointments]);

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'CONFIRMED': return 'bg-green-100 text-green-800';
      case 'SCHEDULED': return 'bg-blue-100 text-blue-800';
      case 'COMPLETED': return 'bg-purple-100 text-purple-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Format appointment time for display
  const formatAppointmentTime = (appointmentDate: string) => {
    const date = new Date(appointmentDate);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <Card key={index} className="animate-pulse">
          <CardHeader>
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-6 w-20" />
            </div>
            <Skeleton className="h-4 w-24" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-16" />
              </div>
              <div className="space-x-2 flex">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-8 w-20" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // Helper function to format date for display
  const formatDateForDisplay = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Helper function to get tab display name
  const getTabDisplayName = (tab: string) => {
    switch (tab) {
      case 'today': return 'Today';
      case 'upcoming': return 'Upcoming';
      case 'completed': return 'Completed';
      default: return tab;
    }
  };

  // Dismiss scheduling info alert
  const dismissSchedulingInfo = () => {
    setSchedulingInfo(null);
  };

  // Modal handlers
  const handleViewDetails = (appointment: AppointmentData) => {
    setSelectedAppointment(appointment);
    setIsViewDetailsOpen(true);
  };

  const handleReschedule = (appointment: AppointmentData) => {
    setSelectedAppointment(appointment);
    setRescheduleDate(appointment.appointmentDate.split('T')[0]);
    setRescheduleTime(formatAppointmentTime(appointment.appointmentDate).replace(/\s(AM|PM)/, ''));
    setRescheduleNotes(appointment.notes || '');
    setIsRescheduleOpen(true);
  };

  const handleViewNotes = (appointment: AppointmentData) => {
    setSelectedAppointment(appointment);
    setIsViewNotesOpen(true);
  };

  const handleCompleteAppointment = (appointment: AppointmentData) => {
    // Update appointment status to completed
    if (appointment.isLocal) {
      setLocalAppointments(prev =>
        prev.map(apt =>
          apt.id === appointment.id
            ? { ...apt, status: 'COMPLETED' }
            : apt
        )
      );
    }

    toast({
      title: "Appointment Completed",
      description: `Appointment with ${appointment.patientName} has been marked as completed.`,
    });
  };

  const handleConfirmAppointment = (appointment: AppointmentData) => {
    // Update appointment status to confirmed
    if (appointment.isLocal) {
      setLocalAppointments(prev =>
        prev.map(apt =>
          apt.id === appointment.id
            ? { ...apt, status: 'CONFIRMED' }
            : apt
        )
      );
    }

    toast({
      title: "Appointment Confirmed",
      description: `Appointment with ${appointment.patientName} has been confirmed.`,
    });
  };

  const handleSaveReschedule = () => {
    if (!rescheduleDate || !rescheduleTime) {
      toast({
        title: "Missing Information",
        description: "Please select both date and time for rescheduling.",
        variant: "destructive",
      });
      return;
    }

    // Update appointment with new date/time
    if (selectedAppointment?.isLocal) {
      const newDateTime = `${rescheduleDate}T${rescheduleTime}:00`;
      setLocalAppointments(prev =>
        prev.map(apt =>
          apt.id === selectedAppointment.id
            ? {
                ...apt,
                appointmentDate: newDateTime,
                notes: rescheduleNotes
              }
            : apt
        )
      );
    }

    toast({
      title: "Appointment Rescheduled",
      description: `Appointment with ${selectedAppointment?.patientName} has been rescheduled.`,
    });

    setIsRescheduleOpen(false);
    setSelectedAppointment(null);
  };

  // Close modal handlers
  const closeModals = () => {
    setIsViewDetailsOpen(false);
    setIsRescheduleOpen(false);
    setIsViewNotesOpen(false);
    setSelectedAppointment(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="pt-20 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Appointment Management</h1>
            <p className="text-gray-600">Manage your daily schedule and patient appointments</p>
          </div>

          {/* Error Alerts */}
          {(todayError || upcomingError) && (
            <Alert className="mb-6 border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <div className="font-medium">Failed to load appointment data</div>
                <div className="text-sm mt-1">
                  {todayErrorDetails?.message || upcomingErrorDetails?.message || 'Unable to fetch appointments from the backend.'}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Scheduling Info Alert */}
          {schedulingInfo && (
            <Alert className="mb-6 border-blue-200 bg-blue-50 relative">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800 pr-8">
                <div className="font-medium">Appointment Scheduled Successfully (Demo Mode)</div>
                <div className="text-sm mt-1">
                  Appointment with <strong>{schedulingInfo.patientName}</strong> scheduled for{' '}
                  <strong>{formatDateForDisplay(schedulingInfo.selectedDate!)}</strong> has been added to the{' '}
                  <strong>{getTabDisplayName(schedulingInfo.targetTab!)}</strong> tab.
                </div>
                <div className="text-xs mt-2 opacity-75">
                  Note: This appointment is stored locally for demonstration. Backend integration pending.
                </div>
              </AlertDescription>
              <Button
                variant="ghost"
                size="sm"
                onClick={dismissSchedulingInfo}
                className="absolute top-2 right-2 h-6 w-6 p-0 text-blue-600 hover:text-blue-800 hover:bg-blue-100"
              >
                <X className="h-4 w-4" />
              </Button>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="today">Today</TabsTrigger>
              <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
            </TabsList>

            <TabsContent value="today" className="space-y-4">
              {todayLoading ? (
                <LoadingSkeleton />
              ) : mergedTodayAppointments.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <p className="text-gray-600 text-center">No appointments scheduled for today</p>
                  </CardContent>
                </Card>
              ) : (
                mergedTodayAppointments.map((appointment) => (
                  <Card key={appointment.id} className={appointment.isLocal ? "border-blue-200 bg-blue-50/30" : ""}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <CardTitle className="text-lg">{appointment.patientName}</CardTitle>
                          {appointment.isLocal && (
                            <Badge variant="outline" className="text-blue-600 border-blue-300">
                              New
                            </Badge>
                          )}
                        </div>
                        <Badge className={getStatusColor(appointment.status)}>
                          {appointment.status}
                        </Badge>
                      </div>
                      <CardDescription>{appointment.reason}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1 text-gray-500" />
                            <span>{formatAppointmentTime(appointment.appointmentDate)}</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                            <span>{appointment.durationMinutes} min</span>
                          </div>
                        </div>
                        <div className="space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDetails(appointment)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                          {appointment.status !== 'COMPLETED' && (
                            <Button
                              size="sm"
                              onClick={() => handleCompleteAppointment(appointment)}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Complete
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </TabsContent>

            <TabsContent value="upcoming" className="space-y-4">
              {upcomingLoading ? (
                <LoadingSkeleton />
              ) : mergedUpcomingAppointments.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <p className="text-gray-600 text-center">No upcoming appointments scheduled</p>
                  </CardContent>
                </Card>
              ) : (
                mergedUpcomingAppointments.map((appointment) => (
                  <Card key={appointment.id} className={appointment.isLocal ? "border-blue-200 bg-blue-50/30" : ""}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <CardTitle className="text-lg">{appointment.patientName}</CardTitle>
                          {appointment.isLocal && (
                            <Badge variant="outline" className="text-blue-600 border-blue-300">
                              New
                            </Badge>
                          )}
                        </div>
                        <Badge className={getStatusColor(appointment.status)}>
                          {appointment.status}
                        </Badge>
                      </div>
                      <CardDescription>{appointment.reason}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1 text-gray-500" />
                            <span>{formatAppointmentTime(appointment.appointmentDate)}</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                            <span>{appointment.durationMinutes} min</span>
                          </div>
                        </div>
                        <div className="space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleReschedule(appointment)}
                          >
                            <CalendarDays className="h-4 w-4 mr-1" />
                            Reschedule
                          </Button>
                          {appointment.status === 'SCHEDULED' && (
                            <Button
                              size="sm"
                              onClick={() => handleConfirmAppointment(appointment)}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Confirm
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </TabsContent>

            <TabsContent value="completed" className="space-y-4">
              {completedAppointments.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <p className="text-gray-600 text-center">No completed appointments</p>
                  </CardContent>
                </Card>
              ) : (
                completedAppointments.map((appointment) => (
                  <Card key={appointment.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{appointment.patientName}</CardTitle>
                        <Badge className={getStatusColor(appointment.status)}>
                          {appointment.status}
                        </Badge>
                      </div>
                      <CardDescription>{appointment.reason}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1 text-gray-500" />
                            <span>{formatAppointmentTime(appointment.appointmentDate)}</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                            <span>{appointment.durationMinutes} min</span>
                          </div>
                        </div>
                        <div className="space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDetails(appointment)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewNotes(appointment)}
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            View Notes
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* View Details Modal */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Appointment Details</DialogTitle>
            <DialogDescription>
              Complete information about this appointment
            </DialogDescription>
          </DialogHeader>

          {selectedAppointment && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Patient Name</Label>
                  <p className="text-sm font-semibold">{selectedAppointment.patientName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Status</Label>
                  <Badge className={getStatusColor(selectedAppointment.status)}>
                    {selectedAppointment.status}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Date</Label>
                  <p className="text-sm">{new Date(selectedAppointment.appointmentDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Time</Label>
                  <p className="text-sm">{formatAppointmentTime(selectedAppointment.appointmentDate)}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Duration</Label>
                  <p className="text-sm">{selectedAppointment.durationMinutes} minutes</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Type</Label>
                  <p className="text-sm">{selectedAppointment.reason || 'Consultation'}</p>
                </div>
              </div>

              {selectedAppointment.notes && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">Notes</Label>
                  <p className="text-sm bg-gray-50 p-3 rounded-md">{selectedAppointment.notes}</p>
                </div>
              )}

              {selectedAppointment.isLocal && (
                <div className="bg-blue-50 p-3 rounded-md">
                  <p className="text-xs text-blue-600">
                    <Info className="h-3 w-3 inline mr-1" />
                    This appointment was created locally and may not be synced with the backend.
                  </p>
                </div>
              )}

              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={closeModals}>
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Reschedule Modal */}
      <Dialog open={isRescheduleOpen} onOpenChange={setIsRescheduleOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Reschedule Appointment</DialogTitle>
            <DialogDescription>
              Change the date and time for {selectedAppointment?.patientName}'s appointment
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="reschedule-date">New Date</Label>
                <Input
                  id="reschedule-date"
                  type="date"
                  value={rescheduleDate}
                  onChange={(e) => setRescheduleDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              <div>
                <Label htmlFor="reschedule-time">New Time</Label>
                <Input
                  id="reschedule-time"
                  type="time"
                  value={rescheduleTime}
                  onChange={(e) => setRescheduleTime(e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="reschedule-notes">Notes (Optional)</Label>
              <Textarea
                id="reschedule-notes"
                placeholder="Add any notes about the rescheduling..."
                value={rescheduleNotes}
                onChange={(e) => setRescheduleNotes(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={closeModals}>
                Cancel
              </Button>
              <Button onClick={handleSaveReschedule}>
                <CalendarDays className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* View Notes Modal */}
      <Dialog open={isViewNotesOpen} onOpenChange={setIsViewNotesOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Appointment Notes</DialogTitle>
            <DialogDescription>
              Notes for {selectedAppointment?.patientName}'s appointment
            </DialogDescription>
          </DialogHeader>

          {selectedAppointment && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-500">Date & Time</Label>
                <p className="text-sm">
                  {new Date(selectedAppointment.appointmentDate).toLocaleDateString()} at{' '}
                  {formatAppointmentTime(selectedAppointment.appointmentDate)}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-500">Notes</Label>
                <div className="bg-gray-50 p-4 rounded-md min-h-[100px]">
                  {selectedAppointment.notes ? (
                    <p className="text-sm whitespace-pre-wrap">{selectedAppointment.notes}</p>
                  ) : (
                    <p className="text-sm text-gray-400 italic">No notes available for this appointment.</p>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={closeModals}>
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AppointmentManagement;
