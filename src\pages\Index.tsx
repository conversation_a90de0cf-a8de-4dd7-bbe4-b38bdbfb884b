
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Heart, Calendar, Users, Shield, ArrowRight, Stethoscope, Clock, MapPin } from "lucide-react";
import Header from "@/components/common/Header";
import Footer from "@/components/common/Footer";

const Index = () => {
  const navigate = useNavigate();
  const [selectedRole, setSelectedRole] = useState("");

  const features = [
    {
      icon: Calendar,
      title: "Easy Appointment Booking",
      description: "Book appointments with your preferred doctors in just a few clicks"
    },
    {
      icon: Users,
      title: "Complete Patient Management",
      description: "Doctors can manage patient records, diagnoses, and treatment plans"
    },
    {
      icon: Stethoscope,
      title: "Digital Medical Records",
      description: "Secure access to your complete medical history and prescriptions"
    },
    {
      icon: Shield,
      title: "HIPAA Compliant Security",
      description: "Your medical data is protected with enterprise-grade security"
    }
  ];

  const roles = [
    {
      id: "PATIENT",
      title: "Patient Portal",
      description: "Book appointments, view medical history, and manage your healthcare",
      color: "bg-blue-50 border-blue-200",
      badge: "bg-blue-100 text-blue-800"
    },
    {
      id: "DOCTOR",
      title: "Doctor Dashboard",
      description: "Manage patients, appointments, and medical records",
      color: "bg-green-50 border-green-200",
      badge: "bg-green-100 text-green-800"
    },
    {
      id: "CLINIC",
      title: "Clinic Management",
      description: "Coordinate staff, manage resources, and oversee operations",
      color: "bg-purple-50 border-purple-200",
      badge: "bg-purple-100 text-purple-800"
    },
    {
      id: "ADMIN",
      title: "System Administration",
      description: "Manage users, approvals, and system-wide settings",
      color: "bg-orange-50 border-orange-200",
      badge: "bg-orange-100 text-orange-800"
    }
  ];

  const stats = [
    { label: "Active Patients", value: "10,000+", icon: Users },
    { label: "Verified Doctors", value: "500+", icon: Stethoscope },
    { label: "Partner Clinics", value: "50+", icon: MapPin },
    { label: "Appointments Booked", value: "25,000+", icon: Clock }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <div className="flex items-center justify-center mb-6">
            <Heart className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-5xl font-bold text-gray-900">MediConnect</h1>
          </div>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Revolutionizing healthcare management with secure, efficient, and user-friendly 
            digital solutions for patients, doctors, and healthcare facilities.
          </p>
          
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <stat.icon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              onClick={() => navigate('/register')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
            >
              Get Started <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              onClick={() => navigate('/login')}
              className="border-blue-200 text-blue-600 hover:bg-blue-50 px-8 py-3"
            >
              Sign In
            </Button>
          </div>
        </div>
      </section>

      {/* Role Selection */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Portal</h2>
            <p className="text-lg text-gray-600">
              Select your role to access specialized features designed for your needs
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {roles.map((role) => (
              <Card 
                key={role.id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${role.color} ${
                  selectedRole === role.id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedRole(role.id)}
              >
                <CardHeader className="text-center">
                  <Badge className={`${role.badge} w-fit mx-auto mb-2`}>
                    {role.title}
                  </Badge>
                  <CardTitle className="text-lg">{role.title}</CardTitle>
                  <CardDescription className="text-sm">
                    {role.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate('/register', { state: { role: role.id } });
                    }}
                  >
                    Register as {role.title.split(' ')[0]}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose MediConnect?</h2>
            <p className="text-lg text-gray-600">
              Experience the future of healthcare management with our comprehensive platform
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center group">
                <div className="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                  <feature.icon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
