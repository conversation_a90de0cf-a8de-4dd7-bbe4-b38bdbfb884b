import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { settingsService, SecuritySettings, SettingsResponse } from '@/services/settingsService';
import { useToast } from '@/hooks/use-toast';

// Query keys for settings
export const settingsKeys = {
  all: ['settings'] as const,
  security: () => [...settingsKeys.all, 'security'] as const,
  auditTrail: () => [...settingsKeys.all, 'auditTrail'] as const,
  metrics: () => [...settingsKeys.all, 'metrics'] as const,
};

// Get current security settings
export const useSecuritySettings = () => {
  return useQuery({
    queryKey: settingsKeys.security(),
    queryFn: () => settingsService.getSecuritySettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Save security settings
export const useSaveSecuritySettings = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (settings: SecuritySettings) => settingsService.saveSecuritySettings(settings),
    onSuccess: (response: SettingsResponse) => {
      if (response.success) {
        // Invalidate and refetch settings
        queryClient.invalidateQueries({ queryKey: settingsKeys.security() });
        queryClient.invalidateQueries({ queryKey: settingsKeys.metrics() });
        queryClient.invalidateQueries({ queryKey: settingsKeys.auditTrail() });
        
        // Show success toast
        toast({
          title: "Settings Saved",
          description: response.message || "Your security settings have been saved successfully.",
        });
        
        console.log('✅ Settings saved successfully');
      } else {
        // Show error toast
        toast({
          title: "Save Failed",
          description: response.message || "Failed to save settings. Please try again.",
          variant: "destructive",
        });
        
        console.error('❌ Settings save failed:', response.message);
      }
    },
    onError: (error: any) => {
      console.error('❌ Settings save error:', error);
      toast({
        title: "Save Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    },
  });
};

// Reset settings to defaults
export const useResetSettings = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: () => settingsService.resetToDefaults(),
    onSuccess: (response: SettingsResponse) => {
      if (response.success) {
        // Invalidate and refetch settings
        queryClient.invalidateQueries({ queryKey: settingsKeys.security() });
        queryClient.invalidateQueries({ queryKey: settingsKeys.metrics() });
        queryClient.invalidateQueries({ queryKey: settingsKeys.auditTrail() });
        
        // Show success toast
        toast({
          title: "Settings Reset",
          description: response.message || "Settings have been reset to defaults.",
        });
        
        console.log('✅ Settings reset successfully');
      } else {
        // Show error toast
        toast({
          title: "Reset Failed",
          description: response.message || "Failed to reset settings. Please try again.",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      console.error('❌ Settings reset error:', error);
      toast({
        title: "Reset Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    },
  });
};

// Get settings audit trail
export const useSettingsAuditTrail = () => {
  return useQuery({
    queryKey: settingsKeys.auditTrail(),
    queryFn: () => settingsService.getSettingsAuditTrail(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
};

// Get security metrics
export const useSecurityMetrics = () => {
  return useQuery({
    queryKey: settingsKeys.metrics(),
    queryFn: () => settingsService.getSecurityMetrics(),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
    retry: 1,
  });
};

// Test settings configuration
export const useTestSettings = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (settings: SecuritySettings) => settingsService.testSettings(settings),
    onSuccess: (result) => {
      if (result.valid) {
        toast({
          title: "Settings Valid",
          description: "All settings are configured correctly.",
        });
      } else {
        toast({
          title: "Settings Issues Found",
          description: `Issues: ${result.issues.join(', ')}`,
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      console.error('❌ Settings test error:', error);
      toast({
        title: "Test Failed",
        description: "Unable to test settings configuration.",
        variant: "destructive",
      });
    },
  });
};

// Export settings
export const useExportSettings = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: () => settingsService.exportSettings(),
    onSuccess: (blob: Blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `mediconnect-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Settings Exported",
        description: "Settings configuration has been downloaded.",
      });
    },
    onError: (error: any) => {
      console.error('❌ Settings export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export settings configuration.",
        variant: "destructive",
      });
    },
  });
};

// Import settings
export const useImportSettings = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (file: File) => settingsService.importSettings(file),
    onSuccess: (response: SettingsResponse) => {
      if (response.success) {
        // Invalidate and refetch settings
        queryClient.invalidateQueries({ queryKey: settingsKeys.security() });
        queryClient.invalidateQueries({ queryKey: settingsKeys.metrics() });
        queryClient.invalidateQueries({ queryKey: settingsKeys.auditTrail() });
        
        toast({
          title: "Settings Imported",
          description: response.message || "Settings have been imported successfully.",
        });
      } else {
        toast({
          title: "Import Failed",
          description: response.message || "Failed to import settings.",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      console.error('❌ Settings import error:', error);
      toast({
        title: "Import Failed",
        description: "Failed to import settings configuration.",
        variant: "destructive",
      });
    },
  });
};

// Combined hook for all settings data
export const useSettingsData = () => {
  const securityQuery = useSecuritySettings();
  const metricsQuery = useSecurityMetrics();
  const auditTrailQuery = useSettingsAuditTrail();

  const isLoading = securityQuery.isLoading || metricsQuery.isLoading;
  const isError = securityQuery.isError || metricsQuery.isError;
  const error = securityQuery.error || metricsQuery.error;

  return {
    // Data
    settings: securityQuery.data,
    metrics: metricsQuery.data,
    auditTrail: auditTrailQuery.data || [],

    // Loading states
    isLoading,
    isError,
    error,

    // Individual query states
    settingsLoading: securityQuery.isLoading,
    metricsLoading: metricsQuery.isLoading,
    auditTrailLoading: auditTrailQuery.isLoading,

    // Refetch functions
    refetchSettings: securityQuery.refetch,
    refetchMetrics: metricsQuery.refetch,
    refetchAuditTrail: auditTrailQuery.refetch,

    // Refetch all
    refetchAll: () => {
      securityQuery.refetch();
      metricsQuery.refetch();
      auditTrailQuery.refetch();
    }
  };
};
