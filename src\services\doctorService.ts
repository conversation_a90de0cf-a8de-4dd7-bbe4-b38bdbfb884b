import { api } from './api';
import { Doctor<PERSON><PERSON><PERSON><PERSON>, Patient, DiagnosisRequest, FollowUpRequest, AppointmentResponse } from '@/types/api';

export const doctorService = {
  // Doctor Profile
  async getProfile(doctorId: number): Promise<DoctorProfile> {
    return await api.get('/doctors/profile', { doctorId });
  },

  async updateProfile(doctorId: number, profileData: Partial<DoctorProfile>): Promise<DoctorProfile> {
    return await api.put('/doctors/profile', profileData, { doctorId });
  },

  // Patient Management
  async getPatients(doctorId: number): Promise<Patient[]> {
    return await api.get('/doctors/patients', { doctorId });
  },

  async getPatientById(patientId: number): Promise<Patient> {
    return await api.get(`/doctors/patients/${patientId}`);
  },

  async searchPatients(doctorId: number, query: string): Promise<Patient[]> {
    return await api.get('/doctors/patients/search', { doctorId, query });
  },

  // Appointments - Updated to match backend team's new endpoints
  async getAppointments(doctorId: number, status?: string, date?: string): Promise<AppointmentResponse[]> {
    const params: any = {};
    if (status) params.status = status;
    if (date) params.date = date;
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/doctor/${doctorId}/appointments${queryString ? `?${queryString}` : ''}`;
    const response = await api.get(endpoint);
    return response.data || response;
  },

  async getTodayAppointments(doctorId: number): Promise<AppointmentResponse[]> {
    const response = await api.get(`/doctor/${doctorId}/appointments/today`);
    return response.data || response;
  },

  async getUpcomingAppointments(doctorId: number): Promise<AppointmentResponse[]> {
    const response = await api.get(`/doctor/${doctorId}/appointments/upcoming`);
    return response.data || response;
  },

  async getCompletedAppointments(doctorId: number): Promise<AppointmentResponse[]> {
    const response = await api.get(`/doctor/${doctorId}/appointments/completed`);
    return response.data || response;
  },

  async updateAppointmentStatus(appointmentId: number, status: string): Promise<void> {
    return await api.put(`/appointments/${appointmentId}/status`, { status });
  },

  async rescheduleAppointment(appointmentId: number, newDate: string, newTime: string, notes?: string): Promise<void> {
    return await api.put(`/appointments/${appointmentId}/reschedule`, {
      newDate,
      newTime,
      notes
    });
  },

  async cancelAppointment(appointmentId: number, reason?: string): Promise<void> {
    const endpoint = `/appointments/${appointmentId}${reason ? `?reason=${encodeURIComponent(reason)}` : ''}`;
    return await api.delete(endpoint);
  },

  async getAppointmentDetails(doctorId: number, appointmentId: number): Promise<AppointmentResponse> {
    const response = await api.get(`/doctor/${doctorId}/appointments/${appointmentId}/details`);
    return response.data || response;
  },

  async getAppointmentNotes(doctorId: number, appointmentId: number): Promise<{ notes: string; diagnosis?: string }> {
    const response = await api.get(`/doctor/${doctorId}/appointments/${appointmentId}/notes`);
    return response.data || response;
  },

  async updateAppointmentNotes(doctorId: number, appointmentId: number, notes: string, diagnosis?: string): Promise<void> {
    return await api.put(`/doctor/${doctorId}/appointments/${appointmentId}/notes`, {
      notes,
      diagnosis
    });
  },

  // Diagnosis
  async createDiagnosis(patientId: number, diagnosisData: DiagnosisRequest): Promise<void> {
    return await api.post(`/doctors/patients/${patientId}/diagnosis`, diagnosisData);
  },

  async getPatientDiagnoses(patientId: number): Promise<any[]> {
    return await api.get(`/doctors/patients/${patientId}/diagnoses`);
  },

  async updateDiagnosis(diagnosisId: number, diagnosisData: Partial<DiagnosisRequest>): Promise<void> {
    return await api.put(`/doctors/diagnoses/${diagnosisId}`, diagnosisData);
  },

  // Follow-ups
  async createFollowUp(followUpData: FollowUpRequest): Promise<void> {
    return await api.post('/doctors/followups', followUpData);
  },

  async getFollowUps(doctorId: number): Promise<any[]> {
    return await api.get('/doctors/followups', { doctorId });
  },

  async updateFollowUp(followUpId: number, followUpData: Partial<FollowUpRequest>): Promise<void> {
    return await api.put(`/doctors/followups/${followUpId}`, followUpData);
  },

  async deleteFollowUp(followUpId: number): Promise<void> {
    return await api.delete(`/doctors/followups/${followUpId}`);
  },

  // Medical Records
  async getPatientMedicalHistory(patientId: number): Promise<any> {
    return await api.get(`/doctors/patients/${patientId}/medical-history`);
  },

  async addMedicalRecord(patientId: number, recordData: any): Promise<void> {
    return await api.post(`/doctors/patients/${patientId}/medical-records`, recordData);
  },

  // Prescriptions
  async createPrescription(patientId: number, prescriptionData: any): Promise<void> {
    return await api.post(`/doctors/patients/${patientId}/prescriptions`, prescriptionData);
  },

  async getPatientPrescriptions(patientId: number): Promise<any[]> {
    return await api.get(`/doctors/patients/${patientId}/prescriptions`);
  },

  async updatePrescription(prescriptionId: number, prescriptionData: any): Promise<void> {
    return await api.put(`/doctors/prescriptions/${prescriptionId}`, prescriptionData);
  },

  // Schedule Management
  async getSchedule(doctorId: number, date?: string): Promise<any> {
    const params: any = { doctorId };
    if (date) params.date = date;
    return await api.get('/doctors/schedule', params);
  },

  async updateSchedule(doctorId: number, scheduleData: any): Promise<void> {
    return await api.put('/doctors/schedule', scheduleData, { doctorId });
  },

  async getAvailability(doctorId: number, date: string): Promise<string[]> {
    return await api.get(`/doctors/${doctorId}/availability`, { date });
  },

  // Statistics
  async getDashboardStats(doctorId: number): Promise<any> {
    return await api.get('/doctors/dashboard/stats', { doctorId });
  },

  async getPatientStats(doctorId: number): Promise<any> {
    return await api.get('/doctors/patients/stats', { doctorId });
  },

  async getAppointmentStats(doctorId: number, period: string = '30d'): Promise<any> {
    return await api.get('/doctors/appointments/stats', { doctorId, period });
  }
};
