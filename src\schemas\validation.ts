import { z } from 'zod';

// Authentication Schemas
export const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export const patientRegisterSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  dateOfBirth: z.string().refine((date) => {
    const birthDate = new Date(date);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    return age >= 0 && age <= 120;
  }, 'Please enter a valid date of birth'),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']),
  role: z.literal('PATIENT'),
  emergencyContactName: z.string().min(2, 'Emergency contact name is required'),
  emergencyContactPhone: z.string().min(10, 'Emergency contact phone is required'),
  bloodGroup: z.string().min(1, 'Blood group is required'),
  allergies: z.string().optional(),
  medicalHistory: z.string().optional(),
});

export const doctorRegisterSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  dateOfBirth: z.string().refine((date) => {
    const birthDate = new Date(date);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    return age >= 18 && age <= 80;
  }, 'Doctor must be between 18 and 80 years old'),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']),
  role: z.literal('DOCTOR'),
  medicalLicense: z.string().min(5, 'Medical license number is required'),
  yearsOfExperience: z.number().min(0, 'Years of experience must be 0 or more').max(60, 'Years of experience cannot exceed 60'),
  qualification: z.string().min(2, 'Qualification is required'),
  consultationFee: z.number().min(0, 'Consultation fee must be 0 or more'),
  bio: z.string().min(10, 'Bio must be at least 10 characters'),
});

// Appointment Schemas
export const appointmentSchema = z.object({
  patientId: z.number().positive('Patient ID is required'),
  doctorId: z.number().positive('Doctor ID is required'),
  appointmentDate: z.string().refine((date) => {
    const appointmentDate = new Date(date);
    const today = new Date();
    return appointmentDate >= today;
  }, 'Appointment date must be in the future'),
  reason: z.string().min(5, 'Reason must be at least 5 characters').max(500, 'Reason cannot exceed 500 characters'),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional(),
  durationMinutes: z.number().min(15, 'Duration must be at least 15 minutes').max(180, 'Duration cannot exceed 180 minutes').optional(),
});

// Profile Update Schemas
export const patientProfileUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  dateOfBirth: z.string().refine((date) => {
    const birthDate = new Date(date);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    return age >= 0 && age <= 120;
  }, 'Please enter a valid date of birth'),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']),
  emergencyContactName: z.string().min(2, 'Emergency contact name is required'),
  emergencyContactPhone: z.string().min(10, 'Emergency contact phone is required'),
  bloodGroup: z.string().min(1, 'Blood group is required'),
  allergies: z.string().optional(),
  medicalHistory: z.string().optional(),
  insuranceProvider: z.string().optional(),
  insuranceNumber: z.string().optional(),
});

export const doctorProfileUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  qualification: z.string().min(2, 'Qualification is required'),
  consultationFee: z.number().min(0, 'Consultation fee must be 0 or more'),
  bio: z.string().min(10, 'Bio must be at least 10 characters'),
  yearsOfExperience: z.number().min(0, 'Years of experience must be 0 or more').max(60, 'Years of experience cannot exceed 60'),
});

// Diagnosis Schema
export const diagnosisSchema = z.object({
  doctorId: z.number().positive('Doctor ID is required'),
  appointmentId: z.number().positive('Appointment ID is required'),
  diagnosisCode: z.string().min(1, 'Diagnosis code is required'),
  diagnosisDescription: z.string().min(5, 'Diagnosis description must be at least 5 characters'),
  symptoms: z.string().min(5, 'Symptoms must be at least 5 characters'),
  treatmentPlan: z.string().min(10, 'Treatment plan must be at least 10 characters'),
  doctorNotes: z.string().min(5, 'Doctor notes must be at least 5 characters'),
});

// Follow-up Schema
export const followUpSchema = z.object({
  patientId: z.number().positive('Patient ID is required'),
  doctorId: z.number().positive('Doctor ID is required'),
  scheduledDate: z.string().refine((date) => {
    const followUpDate = new Date(date);
    const today = new Date();
    return followUpDate >= today;
  }, 'Follow-up date must be in the future'),
  reason: z.string().min(5, 'Reason must be at least 5 characters'),
  notes: z.string().min(5, 'Notes must be at least 5 characters'),
});

// Announcement Schema
export const announcementSchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters').max(100, 'Title cannot exceed 100 characters'),
  content: z.string().min(10, 'Content must be at least 10 characters').max(1000, 'Content cannot exceed 1000 characters'),
  priority: z.enum(['HIGH', 'MEDIUM', 'LOW']),
  startDate: z.string().refine((date) => {
    const startDate = new Date(date);
    const today = new Date();
    return startDate >= today;
  }, 'Start date must be in the future'),
  endDate: z.string(),
}).refine((data) => {
  const startDate = new Date(data.startDate);
  const endDate = new Date(data.endDate);
  return endDate > startDate;
}, {
  message: 'End date must be after start date',
  path: ['endDate'],
});

// Search Schema
export const doctorSearchSchema = z.object({
  specialty: z.string().optional(),
  location: z.string().optional(),
  name: z.string().optional(),
  consultationFee: z.object({
    min: z.number().min(0).optional(),
    max: z.number().min(0).optional(),
  }).optional(),
  experience: z.object({
    min: z.number().min(0).optional(),
    max: z.number().min(0).optional(),
  }).optional(),
});

// Contact Form Schema
export const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

// Export types
export type LoginFormData = z.infer<typeof loginSchema>;
export type PatientRegisterFormData = z.infer<typeof patientRegisterSchema>;
export type DoctorRegisterFormData = z.infer<typeof doctorRegisterSchema>;
export type AppointmentFormData = z.infer<typeof appointmentSchema>;
export type PatientProfileUpdateFormData = z.infer<typeof patientProfileUpdateSchema>;
export type DoctorProfileUpdateFormData = z.infer<typeof doctorProfileUpdateSchema>;
export type DiagnosisFormData = z.infer<typeof diagnosisSchema>;
export type FollowUpFormData = z.infer<typeof followUpSchema>;
export type AnnouncementFormData = z.infer<typeof announcementSchema>;
export type DoctorSearchFormData = z.infer<typeof doctorSearchSchema>;
export type ContactFormData = z.infer<typeof contactFormSchema>;
