import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Loader2, Wifi, WifiOff } from 'lucide-react';
import { api } from '@/services/api';
import { authService } from '@/services/authService';

interface ConnectionTestResult {
  endpoint: string;
  status: 'success' | 'error' | 'loading';
  message: string;
  data?: any;
}

const BackendConnectionTest = () => {
  const [testResults, setTestResults] = useState<ConnectionTestResult[]>([]);
  const [isTestingAll, setIsTestingAll] = useState(false);

  const updateResult = (endpoint: string, status: 'success' | 'error' | 'loading', message: string, data?: any) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.endpoint === endpoint);
      if (existing) {
        return prev.map(r => r.endpoint === endpoint ? { endpoint, status, message, data } : r);
      }
      return [...prev, { endpoint, status, message, data }];
    });
  };

  const testEndpoint = async (endpoint: string, method: 'GET' | 'POST' | 'PUT' = 'GET', data?: any) => {
    updateResult(endpoint, 'loading', 'Testing...');
    
    try {
      let response;
      switch (method) {
        case 'GET':
          response = await api.get(endpoint);
          break;
        case 'POST':
          response = await api.post(endpoint, data);
          break;
        case 'PUT':
          response = await api.put(endpoint, data);
          break;
      }
      
      updateResult(endpoint, 'success', 'Connected successfully', response);
    } catch (error: any) {
      updateResult(endpoint, 'error', error.message || 'Connection failed');
    }
  };

  const testLogin = async () => {
    updateResult('/auth/login', 'loading', 'Testing login...');
    
    try {
      const response = await authService.login({
        email: "<EMAIL>",
        password: "test123"
      });
      
      updateResult('/auth/login', 'success', 'Login successful', {
        token: response.token ? 'Token received' : 'No token',
        user: response.user
      });
    } catch (error: any) {
      updateResult('/auth/login', 'error', error.message || 'Login failed');
    }
  };

  const testAllEndpoints = async () => {
    setIsTestingAll(true);
    setTestResults([]);
    
    // Test login first
    await testLogin();
    
    // Wait a bit for login to complete
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test other endpoints
    await testEndpoint('/admin/users');
    await testEndpoint('/admin/users/1');
    
    setIsTestingAll(false);
  };

  const testCreateUser = async () => {
    const testUserData = {
      name: "Test User",
      email: `test.user.${Date.now()}@example.com`,
      password: "test123",
      phoneNumber: "+**********",
      gender: "OTHER" as const,
      role: "PATIENT" as const
    };
    
    await testEndpoint('/admin/users', 'POST', testUserData);
  };

  const getStatusIcon = (status: 'success' | 'error' | 'loading') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'loading':
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />;
    }
  };

  const getStatusBadge = (status: 'success' | 'error' | 'loading') => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800">Connected</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      case 'loading':
        return <Badge className="bg-blue-100 text-blue-800">Testing...</Badge>;
    }
  };

  const isBackendConnected = testResults.some(r => r.status === 'success');

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isBackendConnected ? (
            <Wifi className="h-5 w-5 text-green-600" />
          ) : (
            <WifiOff className="h-5 w-5 text-red-600" />
          )}
          Backend Connection Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status */}
        <Alert>
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span>
                Backend URL: <code className="bg-gray-100 px-2 py-1 rounded">http://localhost:8083/api</code>
              </span>
              <Badge className={isBackendConnected ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                {isBackendConnected ? "Connected" : "Not Tested"}
              </Badge>
            </div>
          </AlertDescription>
        </Alert>

        {/* Test Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button 
            onClick={testAllEndpoints} 
            disabled={isTestingAll}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isTestingAll ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
            Test All Endpoints
          </Button>
          
          <Button 
            variant="outline" 
            onClick={testLogin}
          >
            Test Login
          </Button>
          
          <Button 
            variant="outline" 
            onClick={() => testEndpoint('/admin/users')}
          >
            Test Get Users
          </Button>
          
          <Button 
            variant="outline" 
            onClick={testCreateUser}
          >
            Test Create User
          </Button>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-medium text-gray-900">Test Results:</h3>
            {testResults.map((result, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  {getStatusIcon(result.status)}
                  <code className="text-sm">{result.endpoint}</code>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(result.status)}
                  <span className="text-sm text-gray-600">{result.message}</span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <Alert>
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">Instructions:</p>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Make sure your Spring Boot backend is running on <code>http://localhost:8083</code></li>
                <li>Click "Test All Endpoints" to verify connection</li>
                <li>If tests pass, your frontend is connected to the backend</li>
                <li>If tests fail, check your backend server and authentication</li>
              </ol>
            </div>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default BackendConnectionTest;
