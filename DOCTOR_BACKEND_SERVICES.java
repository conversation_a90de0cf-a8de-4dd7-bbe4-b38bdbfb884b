// Backend Service Interfaces for Doctor Patients and Appointments
// Copy these interfaces and implementations to your Spring Boot project

// ===== ENTITY CLASSES =====

@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String email;
    
    @Column(nullable = false)
    private String password;
    
    @Column(name = "first_name", nullable = false)
    private String firstName;
    
    @Column(name = "last_name", nullable = false)
    private String lastName;
    
    private String phone;
    
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;
    
    @Enumerated(EnumType.STRING)
    private Gender gender;
    
    private String address;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Role role;
    
    @Enumerated(EnumType.STRING)
    private UserStatus status = UserStatus.ACTIVE;
    
    @Column(name = "profile_image_url")
    private String profileImageUrl;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors, getters, and setters
    public User() {}
    
    // ... getters and setters for all fields
}

@Entity
@Table(name = "patient_profiles")
public class PatientProfile {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "medical_record_number", unique = true)
    private String medicalRecordNumber;
    
    @Column(name = "blood_type")
    private String bloodType;
    
    private String allergies;
    
    @Column(name = "emergency_contact_name")
    private String emergencyContactName;
    
    @Column(name = "emergency_contact_phone")
    private String emergencyContactPhone;
    
    @Column(name = "insurance_provider")
    private String insuranceProvider;
    
    @Column(name = "insurance_policy_number")
    private String insurancePolicyNumber;
    
    @Column(name = "medical_history", columnDefinition = "TEXT")
    private String medicalHistory;
    
    @Column(name = "current_medications", columnDefinition = "TEXT")
    private String currentMedications;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors, getters, and setters
    public PatientProfile() {}
    
    // ... getters and setters for all fields
}

@Entity
@Table(name = "doctor_patient_relationships")
public class DoctorPatientRelationship {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "doctor_id", nullable = false)
    private Long doctorId;
    
    @Column(name = "patient_id", nullable = false)
    private Long patientId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "relationship_status")
    private RelationshipStatus relationshipStatus = RelationshipStatus.ACTIVE;
    
    @CreationTimestamp
    @Column(name = "assigned_date")
    private LocalDateTime assignedDate;
    
    private String notes;
    
    // Constructors, getters, and setters
    public DoctorPatientRelationship() {}
    
    // ... getters and setters for all fields
}

@Entity
@Table(name = "appointments")
public class Appointment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "doctor_id", nullable = false)
    private Long doctorId;
    
    @Column(name = "patient_id", nullable = false)
    private Long patientId;
    
    @Column(name = "appointment_date", nullable = false)
    private LocalDate appointmentDate;
    
    @Column(name = "appointment_time", nullable = false)
    private LocalTime appointmentTime;
    
    @Column(name = "duration_minutes")
    private Integer durationMinutes = 30;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "appointment_type")
    private AppointmentType appointmentType = AppointmentType.CONSULTATION;
    
    @Enumerated(EnumType.STRING)
    private AppointmentStatus status = AppointmentStatus.SCHEDULED;
    
    private String reason;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @Column(columnDefinition = "TEXT")
    private String prescription;
    
    @Column(columnDefinition = "TEXT")
    private String diagnosis;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors, getters, and setters
    public Appointment() {}
    
    // ... getters and setters for all fields
}

// ===== ENUMS =====

public enum Gender {
    MALE, FEMALE, OTHER
}

public enum Role {
    PATIENT, DOCTOR, ADMIN, CLINIC_STAFF, CLINIC
}

public enum UserStatus {
    ACTIVE, INACTIVE, PENDING_APPROVAL
}

public enum RelationshipStatus {
    ACTIVE, INACTIVE
}

public enum AppointmentType {
    CONSULTATION, FOLLOW_UP, EMERGENCY, ROUTINE_CHECKUP
}

public enum AppointmentStatus {
    SCHEDULED, CONFIRMED, IN_PROGRESS, COMPLETED, CANCELLED, NO_SHOW
}

// ===== DTO CLASSES =====

public class PatientDto {
    private Long id;
    private String name;
    private String email;
    private String phone;
    private Integer age;
    private String gender;
    private String lastVisit;
    private String nextAppointment;
    private String status;
    private String allergies;
    private String medicalRecordNumber;
    private String bloodType;
    private String emergencyContact;
    
    // Constructors, getters, and setters
    public PatientDto() {}
    
    // Builder pattern
    public static PatientDtoBuilder builder() {
        return new PatientDtoBuilder();
    }
    
    public static class PatientDtoBuilder {
        private PatientDto patientDto = new PatientDto();
        
        public PatientDtoBuilder id(Long id) {
            patientDto.id = id;
            return this;
        }
        
        public PatientDtoBuilder name(String name) {
            patientDto.name = name;
            return this;
        }
        
        // ... other builder methods
        
        public PatientDto build() {
            return patientDto;
        }
    }
    
    // ... getters and setters for all fields
}

public class PatientStatisticsDto {
    private Integer totalPatients;
    private Integer activePatients;
    private Integer inactivePatients;
    private Double averageAge;
    private Map<String, Integer> genderDistribution;
    private Integer recentPatients;
    private Integer upcomingAppointments;
    
    // Constructors, getters, and setters
    public PatientStatisticsDto() {}
    
    // Builder pattern
    public static PatientStatisticsDtoBuilder builder() {
        return new PatientStatisticsDtoBuilder();
    }
    
    // ... getters and setters for all fields
}

public class AppointmentDto {
    private Long id;
    private Long patientId;
    private String patientName;
    private Long doctorId;
    private String appointmentDate; // ISO format: "2024-01-20T09:00:00"
    private String status;
    private String reason;
    private String notes;
    private Integer durationMinutes;
    private String createdAt;
    
    // Constructors, getters, and setters
    public AppointmentDto() {}
    
    // ... getters and setters for all fields
}

public class AppointmentRequest {
    private String date; // "2025-05-30"
    private String time; // "09:00"
    private Integer duration; // 30
    private String type; // "consultation"
    private String status; // "scheduled"
    private String notes;
    
    // Constructors, getters, and setters
    public AppointmentRequest() {}
    
    // ... getters and setters for all fields
}

public class StatusUpdateRequest {
    private String status;
    
    // Constructors, getters, and setters
    public StatusUpdateRequest() {}
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
}

public class RescheduleRequest {
    private String newDate;
    private String newTime;
    private String notes;

    // Constructors, getters, and setters
    public RescheduleRequest() {}

    // ... getters and setters for all fields
}

// ===== REPOSITORY INTERFACES =====

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByEmail(String email);
    boolean existsByEmail(String email);
    List<User> findByRole(Role role);
    List<User> findByRoleAndStatus(Role role, UserStatus status);
    Long countByRole(Role role);
    Long countByStatus(UserStatus status);
}

@Repository
public interface PatientProfileRepository extends JpaRepository<PatientProfile, Long> {
    Optional<PatientProfile> findByUserId(Long userId);
    boolean existsByUserId(Long userId);
    Optional<PatientProfile> findByMedicalRecordNumber(String medicalRecordNumber);
}

@Repository
public interface DoctorPatientRelationshipRepository extends JpaRepository<DoctorPatientRelationship, Long> {
    List<DoctorPatientRelationship> findByDoctorIdAndRelationshipStatus(Long doctorId, RelationshipStatus status);
    List<DoctorPatientRelationship> findByPatientIdAndRelationshipStatus(Long patientId, RelationshipStatus status);
    boolean existsByDoctorIdAndPatientIdAndRelationshipStatus(Long doctorId, Long patientId, RelationshipStatus status);
    Optional<DoctorPatientRelationship> findByDoctorIdAndPatientId(Long doctorId, Long patientId);
}

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {
    List<Appointment> findByDoctorIdAndAppointmentDateOrderByAppointmentTime(Long doctorId, LocalDate date);
    List<Appointment> findByDoctorIdAndAppointmentDateBetweenOrderByAppointmentDateAscAppointmentTimeAsc(
        Long doctorId, LocalDate startDate, LocalDate endDate);
    List<Appointment> findByPatientIdAndAppointmentDateOrderByAppointmentTime(Long patientId, LocalDate date);
    List<Appointment> findByDoctorIdAndStatus(Long doctorId, AppointmentStatus status);
    List<Appointment> findByPatientIdAndStatus(Long patientId, AppointmentStatus status);
    boolean existsByDoctorIdAndAppointmentDateAndAppointmentTime(Long doctorId, LocalDate date, LocalTime time);

    @Query("SELECT a FROM Appointment a WHERE a.doctorId = :doctorId AND a.appointmentDate = :date AND a.status != 'CANCELLED'")
    List<Appointment> findActiveAppointmentsByDoctorAndDate(@Param("doctorId") Long doctorId, @Param("date") LocalDate date);

    @Query("SELECT a FROM Appointment a WHERE a.patientId = :patientId ORDER BY a.appointmentDate DESC, a.appointmentTime DESC")
    List<Appointment> findPatientAppointmentsOrderByDateDesc(@Param("patientId") Long patientId);

    @Query("SELECT a FROM Appointment a WHERE a.patientId = :patientId AND a.appointmentDate > :date ORDER BY a.appointmentDate ASC, a.appointmentTime ASC")
    List<Appointment> findUpcomingPatientAppointments(@Param("patientId") Long patientId, @Param("date") LocalDate date);
}

// ===== SERVICE INTERFACES =====

public interface DoctorPatientsService {
    List<PatientDto> getPatientsByDoctorId(Long doctorId);
    PatientStatisticsDto getPatientStatistics(Long doctorId);
    boolean isPatientAssignedToDoctor(Long doctorId, Long patientId);
    PatientDto getPatientById(Long patientId);
    void assignPatientToDoctor(Long doctorId, Long patientId);
    void removePatientFromDoctor(Long doctorId, Long patientId);
}

public interface AppointmentService {
    Appointment createAppointment(Long doctorId, Long patientId, AppointmentRequest request);
    List<AppointmentDto> getAppointmentsByDoctorAndDate(Long doctorId, LocalDate date);
    List<AppointmentDto> getAppointmentsByDoctorAndDateRange(Long doctorId, LocalDate startDate, LocalDate endDate);
    List<AppointmentDto> getTodayAppointmentsByDoctor(Long doctorId);
    List<AppointmentDto> getUpcomingAppointmentsByDoctor(Long doctorId);
    Appointment updateAppointmentStatus(Long appointmentId, String status);
    Appointment rescheduleAppointment(Long appointmentId, String newDate, String newTime, String notes);
    boolean isTimeSlotAvailable(Long doctorId, String date, String time);
    void cancelAppointment(Long appointmentId, String reason);
}

// ===== EXCEPTION CLASSES =====

public class ConflictException extends RuntimeException {
    public ConflictException(String message) {
        super(message);
    }
}

public class EntityNotFoundException extends RuntimeException {
    public EntityNotFoundException(String message) {
        super(message);
    }
}

public class ValidationException extends RuntimeException {
    public ValidationException(String message) {
        super(message);
    }
}

// ===== SERVICE IMPLEMENTATIONS =====

@Service
@Transactional
public class DoctorPatientsServiceImpl implements DoctorPatientsService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PatientProfileRepository patientProfileRepository;

    @Autowired
    private DoctorPatientRelationshipRepository relationshipRepository;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Override
    @Transactional(readOnly = true)
    public List<PatientDto> getPatientsByDoctorId(Long doctorId) {
        // Get all active patient relationships for this doctor
        List<DoctorPatientRelationship> relationships =
            relationshipRepository.findByDoctorIdAndRelationshipStatus(doctorId, RelationshipStatus.ACTIVE);

        return relationships.stream()
            .map(relationship -> {
                User patient = userRepository.findById(relationship.getPatientId())
                    .orElseThrow(() -> new EntityNotFoundException("Patient not found"));

                PatientProfile profile = patientProfileRepository.findByUserId(patient.getId())
                    .orElse(null);

                return PatientDto.builder()
                    .id(patient.getId())
                    .name(patient.getFirstName() + " " + patient.getLastName())
                    .email(patient.getEmail())
                    .phone(patient.getPhone())
                    .age(calculateAge(patient.getDateOfBirth()))
                    .gender(patient.getGender() != null ? patient.getGender().toString() : "")
                    .allergies(profile != null ? profile.getAllergies() : "")
                    .bloodType(profile != null ? profile.getBloodType() : "")
                    .medicalRecordNumber(profile != null ? profile.getMedicalRecordNumber() : "")
                    .emergencyContact(profile != null ? profile.getEmergencyContactPhone() : "")
                    .status(patient.getStatus().toString())
                    .lastVisit(getLastVisitDate(patient.getId()))
                    .nextAppointment(getNextAppointmentDate(patient.getId()))
                    .build();
            })
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public PatientStatisticsDto getPatientStatistics(Long doctorId) {
        List<PatientDto> patients = getPatientsByDoctorId(doctorId);

        Map<String, Integer> genderDistribution = new HashMap<>();
        genderDistribution.put("male", 0);
        genderDistribution.put("female", 0);
        genderDistribution.put("other", 0);

        for (PatientDto patient : patients) {
            String gender = patient.getGender().toLowerCase();
            genderDistribution.put(gender, genderDistribution.getOrDefault(gender, 0) + 1);
        }

        return PatientStatisticsDto.builder()
            .totalPatients(patients.size())
            .activePatients((int) patients.stream().filter(p -> "ACTIVE".equals(p.getStatus())).count())
            .inactivePatients((int) patients.stream().filter(p -> "INACTIVE".equals(p.getStatus())).count())
            .averageAge(patients.stream().mapToInt(PatientDto::getAge).average().orElse(0.0))
            .genderDistribution(genderDistribution)
            .recentPatients(getRecentPatientsCount(doctorId))
            .upcomingAppointments(getUpcomingAppointmentsCount(doctorId))
            .build();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isPatientAssignedToDoctor(Long doctorId, Long patientId) {
        return relationshipRepository.existsByDoctorIdAndPatientIdAndRelationshipStatus(
            doctorId, patientId, RelationshipStatus.ACTIVE);
    }

    @Override
    @Transactional(readOnly = true)
    public PatientDto getPatientById(Long patientId) {
        User patient = userRepository.findById(patientId)
            .orElseThrow(() -> new EntityNotFoundException("Patient not found"));

        PatientProfile profile = patientProfileRepository.findByUserId(patientId)
            .orElse(null);

        return PatientDto.builder()
            .id(patient.getId())
            .name(patient.getFirstName() + " " + patient.getLastName())
            .email(patient.getEmail())
            .phone(patient.getPhone())
            .age(calculateAge(patient.getDateOfBirth()))
            .gender(patient.getGender() != null ? patient.getGender().toString() : "")
            .allergies(profile != null ? profile.getAllergies() : "")
            .bloodType(profile != null ? profile.getBloodType() : "")
            .medicalRecordNumber(profile != null ? profile.getMedicalRecordNumber() : "")
            .emergencyContact(profile != null ? profile.getEmergencyContactPhone() : "")
            .status(patient.getStatus().toString())
            .lastVisit(getLastVisitDate(patientId))
            .nextAppointment(getNextAppointmentDate(patientId))
            .build();
    }

    @Override
    public void assignPatientToDoctor(Long doctorId, Long patientId) {
        // Check if relationship already exists
        if (isPatientAssignedToDoctor(doctorId, patientId)) {
            throw new ConflictException("Patient is already assigned to this doctor");
        }

        // Validate doctor and patient exist
        userRepository.findById(doctorId)
            .orElseThrow(() -> new EntityNotFoundException("Doctor not found"));
        userRepository.findById(patientId)
            .orElseThrow(() -> new EntityNotFoundException("Patient not found"));

        DoctorPatientRelationship relationship = new DoctorPatientRelationship();
        relationship.setDoctorId(doctorId);
        relationship.setPatientId(patientId);
        relationship.setRelationshipStatus(RelationshipStatus.ACTIVE);

        relationshipRepository.save(relationship);
    }

    @Override
    public void removePatientFromDoctor(Long doctorId, Long patientId) {
        DoctorPatientRelationship relationship = relationshipRepository
            .findByDoctorIdAndPatientId(doctorId, patientId)
            .orElseThrow(() -> new EntityNotFoundException("Patient relationship not found"));

        relationship.setRelationshipStatus(RelationshipStatus.INACTIVE);
        relationshipRepository.save(relationship);
    }

    // Helper methods
    private Integer calculateAge(LocalDate dateOfBirth) {
        if (dateOfBirth == null) return 0;
        return Period.between(dateOfBirth, LocalDate.now()).getYears();
    }

    private String getLastVisitDate(Long patientId) {
        List<Appointment> appointments = appointmentRepository
            .findPatientAppointmentsOrderByDateDesc(patientId);

        return appointments.stream()
            .filter(apt -> apt.getStatus() == AppointmentStatus.COMPLETED)
            .findFirst()
            .map(apt -> apt.getAppointmentDate().toString())
            .orElse(null);
    }

    private String getNextAppointmentDate(Long patientId) {
        List<Appointment> upcomingAppointments = appointmentRepository
            .findUpcomingPatientAppointments(patientId, LocalDate.now());

        return upcomingAppointments.stream()
            .findFirst()
            .map(apt -> apt.getAppointmentDate().toString())
            .orElse(null);
    }

    private Integer getRecentPatientsCount(Long doctorId) {
        LocalDate thirtyDaysAgo = LocalDate.now().minusDays(30);
        // Implementation depends on your business logic for "recent patients"
        return 8; // Placeholder - implement based on your requirements
    }

    private Integer getUpcomingAppointmentsCount(Long doctorId) {
        LocalDate today = LocalDate.now();
        LocalDate nextWeek = today.plusDays(7);

        List<Appointment> upcomingAppointments = appointmentRepository
            .findByDoctorIdAndAppointmentDateBetweenOrderByAppointmentDateAscAppointmentTimeAsc(
                doctorId, today, nextWeek);

        return upcomingAppointments.size();
    }
}

@Service
@Transactional
public class AppointmentServiceImpl implements AppointmentService {

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DoctorPatientsService doctorPatientsService;

    @Override
    public Appointment createAppointment(Long doctorId, Long patientId, AppointmentRequest request) {
        // Validate doctor and patient exist and are related
        if (!doctorPatientsService.isPatientAssignedToDoctor(doctorId, patientId)) {
            throw new ValidationException("Patient is not assigned to this doctor");
        }

        // Validate time slot availability
        if (!isTimeSlotAvailable(doctorId, request.getDate(), request.getTime())) {
            throw new ConflictException("Time slot is already booked");
        }

        User doctor = userRepository.findById(doctorId)
            .orElseThrow(() -> new EntityNotFoundException("Doctor not found"));
        User patient = userRepository.findById(patientId)
            .orElseThrow(() -> new EntityNotFoundException("Patient not found"));

        Appointment appointment = new Appointment();
        appointment.setDoctorId(doctorId);
        appointment.setPatientId(patientId);
        appointment.setAppointmentDate(LocalDate.parse(request.getDate()));
        appointment.setAppointmentTime(LocalTime.parse(request.getTime()));
        appointment.setDurationMinutes(request.getDuration());
        appointment.setAppointmentType(AppointmentType.valueOf(request.getType().toUpperCase()));
        appointment.setStatus(AppointmentStatus.valueOf(request.getStatus().toUpperCase()));
        appointment.setReason(request.getNotes());
        appointment.setNotes(request.getNotes());

        return appointmentRepository.save(appointment);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AppointmentDto> getAppointmentsByDoctorAndDate(Long doctorId, LocalDate date) {
        List<Appointment> appointments = appointmentRepository
            .findByDoctorIdAndAppointmentDateOrderByAppointmentTime(doctorId, date);

        return appointments.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<AppointmentDto> getAppointmentsByDoctorAndDateRange(Long doctorId, LocalDate startDate, LocalDate endDate) {
        List<Appointment> appointments = appointmentRepository
            .findByDoctorIdAndAppointmentDateBetweenOrderByAppointmentDateAscAppointmentTimeAsc(
                doctorId, startDate, endDate);

        return appointments.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<AppointmentDto> getTodayAppointmentsByDoctor(Long doctorId) {
        LocalDate today = LocalDate.now();
        return getAppointmentsByDoctorAndDate(doctorId, today);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AppointmentDto> getUpcomingAppointmentsByDoctor(Long doctorId) {
        LocalDate tomorrow = LocalDate.now().plusDays(1);
        LocalDate endDate = tomorrow.plusDays(30); // Next 30 days
        return getAppointmentsByDoctorAndDateRange(doctorId, tomorrow, endDate);
    }

    @Override
    public Appointment updateAppointmentStatus(Long appointmentId, String status) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
            .orElseThrow(() -> new EntityNotFoundException("Appointment not found"));

        appointment.setStatus(AppointmentStatus.valueOf(status.toUpperCase()));
        appointment.setUpdatedAt(LocalDateTime.now());

        return appointmentRepository.save(appointment);
    }

    @Override
    public Appointment rescheduleAppointment(Long appointmentId, String newDate, String newTime, String notes) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
            .orElseThrow(() -> new EntityNotFoundException("Appointment not found"));

        // Check if new time slot is available
        if (!isTimeSlotAvailable(appointment.getDoctorId(), newDate, newTime)) {
            throw new ConflictException("New time slot is already booked");
        }

        appointment.setAppointmentDate(LocalDate.parse(newDate));
        appointment.setAppointmentTime(LocalTime.parse(newTime));

        if (notes != null && !notes.trim().isEmpty()) {
            String existingNotes = appointment.getNotes() != null ? appointment.getNotes() : "";
            appointment.setNotes(existingNotes + "\n\nRescheduled: " + notes);
        }

        appointment.setUpdatedAt(LocalDateTime.now());

        return appointmentRepository.save(appointment);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isTimeSlotAvailable(Long doctorId, String date, String time) {
        LocalDate appointmentDate = LocalDate.parse(date);
        LocalTime appointmentTime = LocalTime.parse(time);

        return !appointmentRepository.existsByDoctorIdAndAppointmentDateAndAppointmentTime(
            doctorId, appointmentDate, appointmentTime);
    }

    @Override
    public void cancelAppointment(Long appointmentId, String reason) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
            .orElseThrow(() -> new EntityNotFoundException("Appointment not found"));

        appointment.setStatus(AppointmentStatus.CANCELLED);

        if (reason != null && !reason.trim().isEmpty()) {
            String existingNotes = appointment.getNotes() != null ? appointment.getNotes() : "";
            appointment.setNotes(existingNotes + "\n\nCancelled: " + reason);
        }

        appointment.setUpdatedAt(LocalDateTime.now());

        appointmentRepository.save(appointment);
    }

    // Helper method to convert Appointment entity to DTO
    private AppointmentDto convertToDto(Appointment appointment) {
        // Get patient name
        User patient = userRepository.findById(appointment.getPatientId())
            .orElse(null);
        String patientName = patient != null ?
            patient.getFirstName() + " " + patient.getLastName() : "Unknown Patient";

        AppointmentDto dto = new AppointmentDto();
        dto.setId(appointment.getId());
        dto.setPatientId(appointment.getPatientId());
        dto.setPatientName(patientName);
        dto.setDoctorId(appointment.getDoctorId());

        // Combine date and time into ISO format
        LocalDateTime dateTime = LocalDateTime.of(appointment.getAppointmentDate(), appointment.getAppointmentTime());
        dto.setAppointmentDate(dateTime.toString());

        dto.setStatus(appointment.getStatus().toString());
        dto.setReason(appointment.getReason());
        dto.setNotes(appointment.getNotes());
        dto.setDurationMinutes(appointment.getDurationMinutes());
        dto.setCreatedAt(appointment.getCreatedAt().toString());

        return dto;
    }
}

// ===== REST CONTROLLERS =====

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class DoctorPatientsController {

    @Autowired
    private DoctorPatientsService doctorPatientsService;

    @GetMapping("/doctor/{doctorId}/patients")
    public ResponseEntity<?> getDoctorPatients(@PathVariable Long doctorId) {
        try {
            List<PatientDto> patients = doctorPatientsService.getPatientsByDoctorId(doctorId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Patients retrieved successfully",
                "data", patients
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to retrieve patients: " + e.getMessage(),
                "data", null
            ));
        }
    }

    @GetMapping("/doctor/{doctorId}/patients/statistics")
    public ResponseEntity<?> getDoctorPatientStatistics(@PathVariable Long doctorId) {
        try {
            PatientStatisticsDto stats = doctorPatientsService.getPatientStatistics(doctorId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Patient statistics retrieved successfully",
                "data", stats
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to retrieve statistics: " + e.getMessage(),
                "data", null
            ));
        }
    }

    @GetMapping("/patients/{patientId}")
    public ResponseEntity<?> getPatientById(@PathVariable Long patientId) {
        try {
            PatientDto patient = doctorPatientsService.getPatientById(patientId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Patient retrieved successfully",
                "data", patient
            ));
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(404).body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to retrieve patient: " + e.getMessage(),
                "data", null
            ));
        }
    }

    @PostMapping("/doctor/{doctorId}/patients/{patientId}/assign")
    public ResponseEntity<?> assignPatientToDoctor(
            @PathVariable Long doctorId,
            @PathVariable Long patientId) {
        try {
            doctorPatientsService.assignPatientToDoctor(doctorId, patientId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Patient assigned to doctor successfully",
                "data", null
            ));
        } catch (ConflictException e) {
            return ResponseEntity.status(409).body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(404).body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to assign patient: " + e.getMessage(),
                "data", null
            ));
        }
    }
}

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class AppointmentController {

    @Autowired
    private AppointmentService appointmentService;

    @PostMapping("/doctor/{doctorId}/patients/{patientId}/appointments")
    public ResponseEntity<?> scheduleAppointment(
            @PathVariable Long doctorId,
            @PathVariable Long patientId,
            @RequestBody AppointmentRequest request) {
        try {
            Appointment appointment = appointmentService.createAppointment(doctorId, patientId, request);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Appointment scheduled successfully",
                "data", Map.of(
                    "appointmentId", appointment.getId(),
                    "appointmentDate", appointment.getAppointmentDate().toString(),
                    "appointmentTime", appointment.getAppointmentTime().toString()
                )
            ));
        } catch (ValidationException e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (ConflictException e) {
            return ResponseEntity.status(409).body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(404).body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to schedule appointment: " + e.getMessage(),
                "data", null
            ));
        }
    }

    @GetMapping("/appointments/doctor/{doctorId}/today")
    public ResponseEntity<?> getTodayAppointments(@PathVariable Long doctorId) {
        try {
            List<AppointmentDto> appointments = appointmentService.getTodayAppointmentsByDoctor(doctorId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Today's appointments retrieved successfully",
                "data", appointments
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to retrieve today's appointments: " + e.getMessage(),
                "data", null
            ));
        }
    }

    @GetMapping("/appointments/doctor/{doctorId}/upcoming")
    public ResponseEntity<?> getUpcomingAppointments(@PathVariable Long doctorId) {
        try {
            List<AppointmentDto> appointments = appointmentService.getUpcomingAppointmentsByDoctor(doctorId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Upcoming appointments retrieved successfully",
                "data", appointments
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to retrieve upcoming appointments: " + e.getMessage(),
                "data", null
            ));
        }
    }

    @PutMapping("/appointments/{appointmentId}/status")
    public ResponseEntity<?> updateAppointmentStatus(
            @PathVariable Long appointmentId,
            @RequestBody StatusUpdateRequest request) {
        try {
            Appointment appointment = appointmentService.updateAppointmentStatus(appointmentId, request.getStatus());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Appointment status updated successfully",
                "data", appointment
            ));
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(404).body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to update appointment status: " + e.getMessage(),
                "data", null
            ));
        }
    }

    @PutMapping("/appointments/{appointmentId}/reschedule")
    public ResponseEntity<?> rescheduleAppointment(
            @PathVariable Long appointmentId,
            @RequestBody RescheduleRequest request) {
        try {
            Appointment appointment = appointmentService.rescheduleAppointment(
                appointmentId,
                request.getNewDate(),
                request.getNewTime(),
                request.getNotes()
            );

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Appointment rescheduled successfully",
                "data", appointment
            ));
        } catch (ConflictException e) {
            return ResponseEntity.status(409).body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(404).body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to reschedule appointment: " + e.getMessage(),
                "data", null
            ));
        }
    }

    @DeleteMapping("/appointments/{appointmentId}")
    public ResponseEntity<?> cancelAppointment(
            @PathVariable Long appointmentId,
            @RequestParam(required = false) String reason) {
        try {
            appointmentService.cancelAppointment(appointmentId, reason);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Appointment cancelled successfully",
                "data", null
            ));
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(404).body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "data", null
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to cancel appointment: " + e.getMessage(),
                "data", null
            ));
        }
    }
}
