
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Users, Building2, User<PERSON>heck, AlertTriangle, Activity, FileText, TrendingUp, Shield, Settings, Bell } from "lucide-react";
import Header from "@/components/common/Header";

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const [systemStats] = useState({
    totalUsers: 1234,
    activeClinics: 56,
    pendingApprovals: 12,
    systemAlerts: 3,
    monthlyGrowth: 15.2,
    activeAppointments: 89
  });

  const [recentActivity] = useState([
    {
      id: 1,
      type: "user_registration",
      description: "New doctor registration: Dr. <PERSON>",
      timestamp: "2024-01-15T10:30:00Z",
      status: "pending"
    },
    {
      id: 2,
      type: "clinic_approval",
      description: "Heart Care Center approved and activated",
      timestamp: "2024-01-15T09:15:00Z",
      status: "completed"
    },
    {
      id: 3,
      type: "system_alert",
      description: "High server load detected in region US-East",
      timestamp: "2024-01-15T08:45:00Z",
      status: "warning"
    }
  ]);

  const [pendingApprovals, setPendingApprovals] = useState([
    {
      id: 1,
      type: "doctor",
      name: "Dr. Michael Chen",
      specialty: "Cardiology",
      clinic: "Heart Care Center",
      submittedDate: "2024-01-14",
      status: "pending"
    },
    {
      id: 2,
      type: "clinic",
      name: "Advanced Medical Center",
      location: "New York, NY",
      submittedDate: "2024-01-13",
      status: "under_review"
    }
  ]);

  const [systemAlerts, setSystemAlerts] = useState([
    {
      id: 1,
      severity: "high",
      title: "Database Connection Issues",
      description: "Intermittent connection issues detected",
      timestamp: "2024-01-15T11:00:00Z"
    },
    {
      id: 2,
      severity: "medium",
      title: "High API Usage",
      description: "API rate limit approaching for clinic dashboard",
      timestamp: "2024-01-15T10:15:00Z"
    }
  ]);

  const dashboardStats = [
    { title: "Total Users", value: systemStats.totalUsers.toLocaleString(), icon: Users, color: "text-blue-600", trend: "+12%" },
    { title: "Active Clinics", value: systemStats.activeClinics, icon: Building2, color: "text-green-600", trend: "+5%" },
    { title: "Pending Approvals", value: systemStats.pendingApprovals, icon: UserCheck, color: "text-orange-600", trend: "-2%" },
    { title: "System Alerts", value: systemStats.systemAlerts, icon: AlertTriangle, color: "text-red-600", trend: "+1%" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'under_review': return 'bg-blue-100 text-blue-800';
      case 'warning': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'low': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const approveItem = (id: number, type: string) => {
    setPendingApprovals(prev =>
      prev.filter(item => item.id !== id)
    );
  };

  const resolveAlert = (id: number) => {
    setSystemAlerts(prev =>
      prev.filter(alert => alert.id !== id)
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="pt-20 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                <p className="text-gray-600">Welcome back, {user?.name}</p>
              </div>
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => navigate('/admin/reports')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  View Reports
                </Button>
                <Button
                  onClick={() => navigate('/admin/users')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Manage Users
                </Button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {dashboardStats.map((stat, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">
                    <span className={`${stat.trend.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                      {stat.trend}
                    </span>
                    {" "}from last month
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* System Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="h-5 w-5 mr-2" />
                    System Overview
                  </CardTitle>
                  <CardDescription>Platform health and key metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{systemStats.activeAppointments}</div>
                      <div className="text-sm text-blue-600">Active Appointments</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{systemStats.monthlyGrowth}%</div>
                      <div className="text-sm text-green-600">Monthly Growth</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">99.9%</div>
                      <div className="text-sm text-purple-600">Uptime</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Pending Approvals */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center">
                      <UserCheck className="h-5 w-5 mr-2" />
                      Pending Approvals
                    </span>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => navigate('/admin/doctors')}
                    >
                      View All
                    </Button>
                  </CardTitle>
                  <CardDescription>Items requiring approval</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {pendingApprovals.map((item) => (
                      <div key={item.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h3 className="font-medium">{item.name}</h3>
                            <p className="text-sm text-gray-600">
                              {item.type === 'doctor' ? `${item.specialty} - ${item.clinic}` : item.location}
                            </p>
                          </div>
                          <Badge className={getStatusColor(item.status)}>
                            {item.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <p className="text-sm text-gray-500">
                            Submitted: {new Date(item.submittedDate).toLocaleDateString()}
                          </p>
                          <div className="space-x-2">
                            <Button size="sm" variant="outline">
                              Review
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => approveItem(item.id, item.type)}
                            >
                              Approve
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Bell className="h-5 w-5 mr-2" />
                    Recent Activity
                  </CardTitle>
                  <CardDescription>Latest system events and user actions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentActivity.map((activity) => (
                      <div key={activity.id} className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-900">{activity.description}</p>
                          <div className="flex items-center justify-between mt-1">
                            <p className="text-xs text-gray-500">
                              {new Date(activity.timestamp).toLocaleString()}
                            </p>
                            <Badge className={getStatusColor(activity.status)}>
                              {activity.status}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* System Alerts */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center">
                      <AlertTriangle className="h-5 w-5 mr-2" />
                      System Alerts
                    </span>
                    <Badge variant="outline" className="text-red-600">
                      {systemAlerts.length}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {systemAlerts.map((alert) => (
                      <div key={alert.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm">{alert.title}</h4>
                          <Badge className={getSeverityColor(alert.severity)}>
                            {alert.severity}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-600 mb-2">{alert.description}</p>
                        <div className="flex items-center justify-between">
                          <p className="text-xs text-gray-500">
                            {new Date(alert.timestamp).toLocaleString()}
                          </p>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => resolveAlert(alert.id)}
                          >
                            Resolve
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Settings className="h-5 w-5 mr-2" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => navigate('/admin/users')}
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Manage Users
                    </Button>
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => navigate('/admin/doctors')}
                    >
                      <UserCheck className="h-4 w-4 mr-2" />
                      Doctor Approvals
                    </Button>
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => navigate('/admin/reports')}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      System Reports
                    </Button>
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => navigate('/admin/settings')}
                    >
                      <Shield className="h-4 w-4 mr-2" />
                      Security Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* System Health */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    System Health
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">CPU Usage</span>
                      <span className="text-sm font-medium text-green-600">45%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '45%' }}></div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Memory Usage</span>
                      <span className="text-sm font-medium text-yellow-600">78%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '78%' }}></div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm">API Response</span>
                      <span className="text-sm font-medium text-green-600">120ms</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
