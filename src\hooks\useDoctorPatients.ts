import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { doctorPatientsService, Patient, MedicalRecord, PatientAppointment, PatientStats } from '@/services/doctorPatientsService';
import { useToast } from '@/hooks/use-toast';

// Query keys for doctor patients
export const doctorPatientsKeys = {
  all: ['doctorPatients'] as const,
  patients: (doctorId: number) => [...doctorPatientsKeys.all, 'patients', doctorId] as const,
  patient: (patientId: number) => [...doctorPatientsKeys.all, 'patient', patientId] as const,
  patientHistory: (patientId: number) => [...doctorPatientsKeys.all, 'patient', patientId, 'history'] as const,
  patientAppointments: (patientId: number, doctorId: number) => [...doctorPatientsKeys.all, 'patient', patientId, 'appointments', doctorId] as const,
  patientStats: (doctorId: number) => [...doctorPatientsKeys.all, 'stats', doctorId] as const,
  search: (doctorId: number, query: string) => [...doctorPatientsKeys.all, 'search', doctorId, query] as const,
};

// Get all patients for a doctor
export const useDoctorPatients = (doctorId: number, searchQuery?: string, status?: string) => {
  return useQuery({
    queryKey: doctorPatientsKeys.patients(doctorId),
    queryFn: () => doctorPatientsService.getPatients(doctorId, searchQuery, status),
    enabled: !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Get patient details by ID
export const usePatientDetails = (patientId: number) => {
  return useQuery({
    queryKey: doctorPatientsKeys.patient(patientId),
    queryFn: () => doctorPatientsService.getPatientById(patientId),
    enabled: !!patientId,
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Get patient's medical history
export const usePatientMedicalHistory = (patientId: number) => {
  return useQuery({
    queryKey: doctorPatientsKeys.patientHistory(patientId),
    queryFn: () => doctorPatientsService.getPatientMedicalHistory(patientId),
    enabled: !!patientId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Get patient's appointments
export const usePatientAppointments = (patientId: number, doctorId: number) => {
  return useQuery({
    queryKey: doctorPatientsKeys.patientAppointments(patientId, doctorId),
    queryFn: () => doctorPatientsService.getPatientAppointments(patientId, doctorId),
    enabled: !!patientId && !!doctorId,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
    retry: 2,
  });
};

// Get patient statistics
export const usePatientStats = (doctorId: number) => {
  return useQuery({
    queryKey: doctorPatientsKeys.patientStats(doctorId),
    queryFn: () => doctorPatientsService.getPatientStats(doctorId),
    enabled: !!doctorId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Search patients
export const useSearchPatients = (doctorId: number, query: string) => {
  return useQuery({
    queryKey: doctorPatientsKeys.search(doctorId, query),
    queryFn: () => doctorPatientsService.searchPatients(doctorId, query),
    enabled: !!doctorId && !!query && query.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    retry: 1,
  });
};

// Schedule appointment mutation
export const useScheduleAppointment = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (appointment: Omit<PatientAppointment, 'id'>) =>
      doctorPatientsService.scheduleAppointment(appointment),
    onSuccess: (result, variables) => {
      if (result.success) {
        // Invalidate and refetch related queries
        queryClient.invalidateQueries({ 
          queryKey: doctorPatientsKeys.patientAppointments(variables.patientId, variables.doctorId) 
        });
        queryClient.invalidateQueries({ 
          queryKey: doctorPatientsKeys.patientStats(variables.doctorId) 
        });

        // Show success toast
        toast({
          title: "Appointment Scheduled",
          description: result.message,
        });

        console.log('✅ Appointment scheduled successfully');
      } else {
        // Show error toast
        toast({
          title: "Scheduling Failed",
          description: result.message,
          variant: "destructive",
        });

        console.error('❌ Appointment scheduling failed:', result.message);
      }
    },
    onError: (error: any) => {
      console.error('❌ Appointment scheduling error:', error);

      // Handle specific error types
      const errorMessage = error.message || error.toString();
      let title = "Scheduling Failed";
      let description = "An unexpected error occurred while scheduling the appointment.";

      if (errorMessage.includes("Appointment slot not available")) {
        title = "Time Slot Unavailable";
        description = "The selected time slot is already booked. Please choose a different time.";
      } else if (errorMessage.includes("clinic_id")) {
        title = "Configuration Error";
        description = "Doctor clinic information is missing. Please contact administrator.";
      } else if (errorMessage.includes("Patient is not assigned")) {
        title = "Patient Assignment Error";
        description = "Patient is not assigned to this doctor. Please contact administrator.";
      } else if (errorMessage.includes("Invalid date")) {
        title = "Invalid Date";
        description = "The selected date is invalid. Please choose a valid future date.";
      } else if (errorMessage.includes("Outside working hours")) {
        title = "Outside Working Hours";
        description = "The selected time is outside doctor's working hours.";
      }

      toast({
        title,
        description,
        variant: "destructive",
      });
    },
  });
};

// Update patient mutation
export const useUpdatePatient = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ patientId, updates }: { patientId: number; updates: Partial<Patient> }) =>
      doctorPatientsService.updatePatient(patientId, updates),
    onSuccess: (result, { patientId }) => {
      if (result.success) {
        // Invalidate and refetch related queries
        queryClient.invalidateQueries({ queryKey: doctorPatientsKeys.patient(patientId) });
        queryClient.invalidateQueries({ queryKey: doctorPatientsKeys.all });

        // Show success toast
        toast({
          title: "Patient Updated",
          description: result.message,
        });

        console.log('✅ Patient updated successfully');
      } else {
        // Show error toast
        toast({
          title: "Update Failed",
          description: result.message,
          variant: "destructive",
        });

        console.error('❌ Patient update failed:', result.message);
      }
    },
    onError: (error: any) => {
      console.error('❌ Patient update error:', error);
      toast({
        title: "Update Failed",
        description: "An unexpected error occurred while updating patient information.",
        variant: "destructive",
      });
    },
  });
};

// Add medical record mutation
export const useAddMedicalRecord = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ patientId, record }: { patientId: number; record: Omit<MedicalRecord, 'id'> }) =>
      doctorPatientsService.addMedicalRecord(patientId, record),
    onSuccess: (result, { patientId }) => {
      if (result.success) {
        // Invalidate and refetch related queries
        queryClient.invalidateQueries({ queryKey: doctorPatientsKeys.patientHistory(patientId) });
        queryClient.invalidateQueries({ queryKey: doctorPatientsKeys.patient(patientId) });

        // Show success toast
        toast({
          title: "Medical Record Added",
          description: result.message,
        });

        console.log('✅ Medical record added successfully');
      } else {
        // Show error toast
        toast({
          title: "Failed to Add Record",
          description: result.message,
          variant: "destructive",
        });

        console.error('❌ Medical record addition failed:', result.message);
      }
    },
    onError: (error: any) => {
      console.error('❌ Medical record addition error:', error);
      toast({
        title: "Failed to Add Record",
        description: "An unexpected error occurred while adding the medical record.",
        variant: "destructive",
      });
    },
  });
};

// Combined hook for all patient data
export const useDoctorPatientsData = (doctorId: number) => {
  const patientsQuery = useDoctorPatients(doctorId);
  const statsQuery = usePatientStats(doctorId);

  const isLoading = patientsQuery.isLoading || statsQuery.isLoading;
  const isError = patientsQuery.isError || statsQuery.isError;
  const error = patientsQuery.error || statsQuery.error;

  return {
    // Data
    patients: patientsQuery.data || [],
    stats: statsQuery.data,

    // Loading states
    isLoading,
    isError,
    error,

    // Individual loading states
    patientsLoading: patientsQuery.isLoading,
    statsLoading: statsQuery.isLoading,

    // Refetch functions
    refetchPatients: patientsQuery.refetch,
    refetchStats: statsQuery.refetch,

    // Refetch all
    refetchAll: () => {
      patientsQuery.refetch();
      statsQuery.refetch();
    }
  };
};

// Hook for patient search with debouncing
export const useDebouncedPatientSearch = (doctorId: number, query: string, delay: number = 300) => {
  const [debouncedQuery, setDebouncedQuery] = useState(query);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, delay);

    return () => clearTimeout(timer);
  }, [query, delay]);

  return useSearchPatients(doctorId, debouncedQuery);
};
