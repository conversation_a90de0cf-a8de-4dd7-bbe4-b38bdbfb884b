
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Heart, Menu, X } from "lucide-react";
import { useAuth } from "@/context/AuthContext";

const Header = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const getRoleBasedPath = () => {
    if (!user) return '/';
    switch (user.role) {
      case 'PATIENT': return '/patient';
      case 'DOCTOR': return '/doctor';
      case 'CLINIC': return '/clinic';
      case 'ADMIN': return '/admin';
      default: return '/';
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-blue-100 fixed w-full top-0 z-50">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div 
            className="flex items-center cursor-pointer" 
            onClick={() => navigate('/')}
          >
            <Heart className="h-8 w-8 text-blue-600 mr-2" />
            <span className="text-xl font-bold text-gray-900">MediConnect</span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {user ? (
              <>
                <Button 
                  variant="ghost" 
                  onClick={() => navigate(getRoleBasedPath())}
                  className="text-gray-600 hover:text-blue-600"
                >
                  Dashboard
                </Button>
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-600">
                    Welcome, {user.name}
                  </span>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleLogout}
                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                  >
                    Logout
                  </Button>
                </div>
              </>
            ) : (
              <>
                <Button 
                  variant="ghost" 
                  onClick={() => navigate('/login')}
                  className="text-gray-600 hover:text-blue-600"
                >
                  Sign In
                </Button>
                <Button 
                  onClick={() => navigate('/register')}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Get Started
                </Button>
              </>
            )}
          </nav>

          {/* Mobile menu button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X className="h-6 w-6 text-gray-600" />
            ) : (
              <Menu className="h-6 w-6 text-gray-600" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            {user ? (
              <div className="space-y-2">
                <Button 
                  variant="ghost" 
                  onClick={() => {
                    navigate(getRoleBasedPath());
                    setIsMenuOpen(false);
                  }}
                  className="w-full justify-start text-gray-600 hover:text-blue-600"
                >
                  Dashboard
                </Button>
                <div className="px-3 py-2 text-sm text-gray-600">
                  Welcome, {user.name}
                </div>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    handleLogout();
                    setIsMenuOpen(false);
                  }}
                  className="w-full border-blue-200 text-blue-600 hover:bg-blue-50"
                >
                  Logout
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <Button 
                  variant="ghost" 
                  onClick={() => {
                    navigate('/login');
                    setIsMenuOpen(false);
                  }}
                  className="w-full justify-start text-gray-600 hover:text-blue-600"
                >
                  Sign In
                </Button>
                <Button 
                  onClick={() => {
                    navigate('/register');
                    setIsMenuOpen(false);
                  }}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Get Started
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
