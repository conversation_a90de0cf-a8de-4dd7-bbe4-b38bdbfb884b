// Backend Service Interfaces for PDF Export Functionality
// Copy these interfaces to your Spring Boot project

// ===== USER SERVICE INTERFACE =====
public interface UserService {
    
    // Basic user operations (already implemented)
    User save(User user);
    Optional<User> findById(Long id);
    List<User> findAll();
    boolean existsByEmail(String email);
    
    // Statistics methods for PDF export
    Long getTotalUsers();
    Long getActiveUsersCount();
    Long getPendingUsersCount();
    Long getInactiveUsersCount();
    
    // Role-based statistics
    Long getUserCountByRole(Role role);
    Long getUsersCreatedInPeriod(Role role, LocalDateTime start, LocalDateTime end);
    
    // Growth analytics
    List<User> getUsersCreatedAfter(LocalDateTime date);
    Map<String, Long> getUserRegistrationsByMonth(int months);
}

// ===== REPORTS SERVICE INTERFACE =====
public interface ReportsService {
    
    // System statistics
    SystemStatsResponse getSystemStats(int days);
    List<UserGrowthData> getUserGrowthData(int months);
    PerformanceMetrics getPerformanceMetrics();
    
    // Optional: If you have appointments
    List<AppointmentAnalytics> getAppointmentAnalytics(int days);
    
    // Optional: If you have revenue tracking
    RevenueAnalytics getRevenueAnalytics(int days);
}

// ===== APPOINTMENT SERVICE INTERFACE (Optional) =====
public interface AppointmentService {
    
    Long getTotalAppointments();
    Long getCompletedAppointments();
    Long getPendingAppointments();
    Long getCancelledAppointments();
    Long getAppointmentCountByStatus(AppointmentStatus status, LocalDateTime since);
}

// ===== MONITORING SERVICE INTERFACE (Optional) =====
public interface MonitoringService {
    
    String getAverageApiResponseTime();
    String getAverageDatabaseQueryTime();
    String getErrorRate();
    String getSystemUptime();
    String getCpuUsage();
    String getMemoryUsage();
    String getDiskUsage();
}

// ===== SESSION SERVICE INTERFACE (Optional) =====
public interface SessionService {
    
    Long getActiveSessionsCount();
    List<ActiveSession> getActiveSessions();
    void invalidateSession(String sessionId);
}

// ===== REVENUE SERVICE INTERFACE (Optional) =====
public interface RevenueService {
    
    BigDecimal getMonthlyRecurringRevenue();
    BigDecimal getTotalSubscriptionRevenue(LocalDateTime since);
    BigDecimal getAverageRevenuePerUser();
    Double getChurnRate();
    Long getNewSubscriptions(LocalDateTime since);
    Long getCancelledSubscriptions(LocalDateTime since);
}

// ===== REPOSITORY INTERFACES =====

// Enhanced User Repository
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    // Existing methods
    Optional<User> findByEmail(String email);
    boolean existsByEmail(String email);
    
    // Statistics methods for PDF export
    Long countByStatus(UserStatus status);
    Long countByRole(Role role);
    Long countByRoleAndStatus(Role role, UserStatus status);
    Long countByRoleAndCreatedAtBetween(Role role, LocalDateTime start, LocalDateTime end);
    
    // Growth analytics
    List<User> findByCreatedAtAfter(LocalDateTime date);
    List<User> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);
    List<User> findByRoleAndCreatedAtBetween(Role role, LocalDateTime start, LocalDateTime end);
    
    // Aggregation queries
    @Query("SELECT u.role, COUNT(u) FROM User u GROUP BY u.role")
    List<Object[]> countUsersByRole();
    
    @Query("SELECT u.status, COUNT(u) FROM User u GROUP BY u.status")
    List<Object[]> countUsersByStatus();
    
    @Query("SELECT DATE_FORMAT(u.createdAt, '%Y-%m'), COUNT(u) FROM User u " +
           "WHERE u.createdAt >= :startDate GROUP BY DATE_FORMAT(u.createdAt, '%Y-%m')")
    List<Object[]> getUserRegistrationsByMonth(@Param("startDate") LocalDateTime startDate);
}

// Appointment Repository (Optional)
@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {
    
    Long countByStatus(AppointmentStatus status);
    Long countByStatusAndCreatedAtAfter(AppointmentStatus status, LocalDateTime since);
    List<Appointment> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);
    
    @Query("SELECT a.status, COUNT(a) FROM Appointment a GROUP BY a.status")
    List<Object[]> countAppointmentsByStatus();
    
    @Query("SELECT DATE_FORMAT(a.createdAt, '%Y-%m'), COUNT(a) FROM Appointment a " +
           "WHERE a.createdAt >= :startDate GROUP BY DATE_FORMAT(a.createdAt, '%Y-%m')")
    List<Object[]> getAppointmentsByMonth(@Param("startDate") LocalDateTime startDate);
}

// ===== DTO CLASSES =====

// System Statistics Response
public class SystemStatsResponse {
    // User statistics
    private Long totalUsers;
    private Long activeUsers;
    private Long pendingUsers;
    private Long inactiveUsers;
    
    // Role distribution
    private Long doctorCount;
    private Long patientCount;
    private Long clinicCount;
    private Long clinicStaffCount;
    private Long adminCount;
    
    // Appointment statistics (optional)
    private Long totalAppointments;
    private Long completedAppointments;
    private Long pendingAppointments;
    private Long cancelledAppointments;
    
    // System metrics
    private String systemUptime;
    private String apiResponseTime;
    private String databaseQueryTime;
    private String errorRate;
    private Long activeSessions;
    
    // Constructors, getters, and setters
    public SystemStatsResponse() {}
    
    // ... getters and setters for all fields
}

// User Growth Data
public class UserGrowthData {
    private String month;
    private Long patients;
    private Long doctors;
    private Long clinics;
    private Long clinicStaff;
    private Long admins;
    private Long total;
    
    public UserGrowthData() {}
    
    public UserGrowthData(String month, Long patients, Long doctors, Long clinics, Long clinicStaff) {
        this.month = month;
        this.patients = patients;
        this.doctors = doctors;
        this.clinics = clinics;
        this.clinicStaff = clinicStaff;
        this.total = patients + doctors + clinics + clinicStaff;
    }
    
    // ... getters and setters
}

// Appointment Analytics
public class AppointmentAnalytics {
    private String status;
    private Long count;
    private Double percentage;
    
    public AppointmentAnalytics() {}
    
    public AppointmentAnalytics(String status, Long count) {
        this.status = status;
        this.count = count;
    }
    
    // ... getters and setters
}

// Performance Metrics
public class PerformanceMetrics {
    private String apiResponseTime;
    private String databaseQueryTime;
    private String errorRate;
    private Long activeSessions;
    private String systemUptime;
    private String cpuUsage;
    private String memoryUsage;
    private String diskUsage;
    
    // ... getters and setters
}

// Revenue Analytics (Optional)
public class RevenueAnalytics {
    private BigDecimal monthlyRecurringRevenue;
    private BigDecimal totalSubscriptionRevenue;
    private BigDecimal averageRevenuePerUser;
    private Double churnRate;
    private Long newSubscriptions;
    private Long cancelledSubscriptions;
    
    // ... getters and setters
}

// ===== IMPLEMENTATION EXAMPLE =====

@Service
@Transactional(readOnly = true)
public class ReportsServiceImpl implements ReportsService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private AppointmentRepository appointmentRepository; // Optional
    
    @Override
    public SystemStatsResponse getSystemStats(int days) {
        SystemStatsResponse stats = new SystemStatsResponse();
        
        // User statistics
        stats.setTotalUsers(userRepository.count());
        stats.setActiveUsers(userRepository.countByStatus(UserStatus.ACTIVE));
        stats.setPendingUsers(userRepository.countByStatus(UserStatus.PENDING_APPROVAL));
        stats.setInactiveUsers(userRepository.countByStatus(UserStatus.INACTIVE));
        
        // Role distribution
        stats.setDoctorCount(userRepository.countByRole(Role.DOCTOR));
        stats.setPatientCount(userRepository.countByRole(Role.PATIENT));
        stats.setClinicCount(userRepository.countByRole(Role.CLINIC));
        stats.setClinicStaffCount(userRepository.countByRole(Role.CLINIC_STAFF));
        stats.setAdminCount(userRepository.countByRole(Role.ADMIN));
        
        // System metrics (implement based on your monitoring)
        stats.setSystemUptime("99.9%");
        stats.setApiResponseTime("245ms");
        stats.setDatabaseQueryTime("89ms");
        stats.setErrorRate("0.02%");
        stats.setActiveSessions(100L); // Implement session tracking
        
        return stats;
    }
    
    @Override
    public List<UserGrowthData> getUserGrowthData(int months) {
        List<UserGrowthData> growthData = new ArrayList<>();
        LocalDateTime startDate = LocalDateTime.now().minusMonths(months);
        
        for (int i = 0; i < months; i++) {
            LocalDateTime monthStart = startDate.plusMonths(i);
            LocalDateTime monthEnd = monthStart.plusMonths(1);
            
            String monthName = monthStart.format(DateTimeFormatter.ofPattern("MMM yyyy"));
            Long patients = userRepository.countByRoleAndCreatedAtBetween(Role.PATIENT, monthStart, monthEnd);
            Long doctors = userRepository.countByRoleAndCreatedAtBetween(Role.DOCTOR, monthStart, monthEnd);
            Long clinics = userRepository.countByRoleAndCreatedAtBetween(Role.CLINIC, monthStart, monthEnd);
            Long clinicStaff = userRepository.countByRoleAndCreatedAtBetween(Role.CLINIC_STAFF, monthStart, monthEnd);
            
            growthData.add(new UserGrowthData(monthName, patients, doctors, clinics, clinicStaff));
        }
        
        return growthData;
    }
    
    @Override
    public PerformanceMetrics getPerformanceMetrics() {
        PerformanceMetrics metrics = new PerformanceMetrics();
        
        // Implement based on your monitoring system
        metrics.setApiResponseTime("245ms avg");
        metrics.setDatabaseQueryTime("89ms avg");
        metrics.setErrorRate("0.02%");
        metrics.setActiveSessions(100L);
        metrics.setSystemUptime("99.9%");
        metrics.setCpuUsage("45%");
        metrics.setMemoryUsage("67%");
        metrics.setDiskUsage("23%");
        
        return metrics;
    }
}
