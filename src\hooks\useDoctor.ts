import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { doctorService } from '@/services/doctorService';
import { DoctorProfile, Patient, DiagnosisRequest, FollowUpRequest, AppointmentResponse } from '@/types/api';

// Query Keys
export const doctorKeys = {
  all: ['doctor'] as const,
  profile: (id: number) => [...doctorKeys.all, 'profile', id] as const,
  patients: (id: number) => [...doctorKeys.all, 'patients', id] as const,
  appointments: (id: number) => [...doctorKeys.all, 'appointments', id] as const,
  todayAppointments: (id: number) => [...doctorKeys.all, 'appointments', 'today', id] as const,
  upcomingAppointments: (id: number) => [...doctorKeys.all, 'appointments', 'upcoming', id] as const,
  followUps: (id: number) => [...doctorKeys.all, 'followups', id] as const,
  patientDiagnoses: (patientId: number) => [...doctorKeys.all, 'patient-diagnoses', patientId] as const,
  patientMedicalHistory: (patientId: number) => [...doctorKeys.all, 'patient-medical-history', patientId] as const,
  dashboardStats: (id: number) => [...doctorKeys.all, 'dashboard-stats', id] as const,
  schedule: (id: number, date?: string) => [...doctorKeys.all, 'schedule', id, date] as const,
};

// Doctor Profile Hooks
export const useDoctorProfile = (doctorId: number) => {
  return useQuery({
    queryKey: doctorKeys.profile(doctorId),
    queryFn: () => doctorService.getProfile(doctorId),
    enabled: !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateDoctorProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ doctorId, profileData }: { doctorId: number; profileData: Partial<DoctorProfile> }) =>
      doctorService.updateProfile(doctorId, profileData),
    onSuccess: (data, variables) => {
      queryClient.setQueryData(doctorKeys.profile(variables.doctorId), data);
      queryClient.invalidateQueries({ queryKey: doctorKeys.profile(variables.doctorId) });
    },
  });
};

// Patient Management Hooks
export const useDoctorPatients = (doctorId: number) => {
  return useQuery({
    queryKey: doctorKeys.patients(doctorId),
    queryFn: () => doctorService.getPatients(doctorId),
    enabled: !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const usePatientById = (patientId: number) => {
  return useQuery({
    queryKey: ['patient', patientId],
    queryFn: () => doctorService.getPatientById(patientId),
    enabled: !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Appointment Management Hooks
export const useDoctorAppointments = (doctorId: number, status?: string, date?: string) => {
  return useQuery({
    queryKey: doctorKeys.appointments(doctorId),
    queryFn: () => doctorService.getAppointments(doctorId, status, date),
    enabled: !!doctorId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useDoctorTodayAppointments = (doctorId: number) => {
  return useQuery({
    queryKey: doctorKeys.todayAppointments(doctorId),
    queryFn: () => doctorService.getTodayAppointments(doctorId),
    enabled: !!doctorId,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};

export const useDoctorUpcomingAppointments = (doctorId: number) => {
  return useQuery({
    queryKey: doctorKeys.upcomingAppointments(doctorId),
    queryFn: () => doctorService.getUpcomingAppointments(doctorId),
    enabled: !!doctorId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useUpdateAppointmentStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ appointmentId, status }: { appointmentId: number; status: string }) =>
      doctorService.updateAppointmentStatus(appointmentId, status),
    onSuccess: () => {
      // Invalidate all appointment-related queries
      queryClient.invalidateQueries({ queryKey: doctorKeys.all });
    },
  });
};

// Diagnosis Hooks
export const useCreateDiagnosis = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ patientId, diagnosisData }: { patientId: number; diagnosisData: DiagnosisRequest }) =>
      doctorService.createDiagnosis(patientId, diagnosisData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: doctorKeys.patientDiagnoses(variables.patientId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: doctorKeys.patientMedicalHistory(variables.patientId) 
      });
    },
  });
};

export const usePatientDiagnoses = (patientId: number) => {
  return useQuery({
    queryKey: doctorKeys.patientDiagnoses(patientId),
    queryFn: () => doctorService.getPatientDiagnoses(patientId),
    enabled: !!patientId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Follow-up Hooks
export const useDoctorFollowUps = (doctorId: number) => {
  return useQuery({
    queryKey: doctorKeys.followUps(doctorId),
    queryFn: () => doctorService.getFollowUps(doctorId),
    enabled: !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateFollowUp = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (followUpData: FollowUpRequest) =>
      doctorService.createFollowUp(followUpData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: doctorKeys.followUps(variables.doctorId) 
      });
    },
  });
};

// Medical History Hook
export const usePatientMedicalHistory = (patientId: number) => {
  return useQuery({
    queryKey: doctorKeys.patientMedicalHistory(patientId),
    queryFn: () => doctorService.getPatientMedicalHistory(patientId),
    enabled: !!patientId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Dashboard Stats Hook
export const useDoctorDashboardStats = (doctorId: number) => {
  return useQuery({
    queryKey: doctorKeys.dashboardStats(doctorId),
    queryFn: () => doctorService.getDashboardStats(doctorId),
    enabled: !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Schedule Hook
export const useDoctorSchedule = (doctorId: number, date?: string) => {
  return useQuery({
    queryKey: doctorKeys.schedule(doctorId, date),
    queryFn: () => doctorService.getSchedule(doctorId, date),
    enabled: !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
