import { api } from './api';

export interface SecuritySettings {
  twoFactorAuthEnabled: boolean;
  strongPasswordPolicyEnabled: boolean;
  sessionTimeoutMinutes: number;
  maxLoginAttempts: number;
  dataEncryptionEnabled: boolean;
  auditLoggingEnabled: boolean;
}

export interface SettingsResponse {
  success: boolean;
  message: string;
  data?: SecuritySettings;
}

export const settingsService = {
  // Get current security settings
  async getSecuritySettings(): Promise<SecuritySettings> {
    try {
      console.log('🚀 Fetching security settings from backend...');
      const response = await api.get('/admin/settings/security');
      console.log('✅ Security settings fetched successfully:', response);
      
      // Handle different response formats
      if (response && response.data) {
        return response.data;
      } else if (response && typeof response === 'object') {
        return response;
      }
      
      // Fallback to default settings
      console.warn('⚠️ Using default settings as fallback');
      return this.getDefaultSettings();
    } catch (error: any) {
      console.error('❌ Failed to fetch security settings:', error);
      console.warn('⚠️ Using default settings as fallback');
      return this.getDefaultSettings();
    }
  },

  // Save security settings
  async saveSecuritySettings(settings: SecuritySettings): Promise<SettingsResponse> {
    try {
      console.log('🚀 Saving security settings to backend...', settings);
      
      // Validate settings before sending
      const validatedSettings = this.validateSettings(settings);
      
      const response = await api.post('/admin/settings/security', validatedSettings);
      console.log('✅ Security settings saved successfully:', response);
      
      return {
        success: true,
        message: response.message || 'Settings saved successfully',
        data: response.data || validatedSettings
      };
    } catch (error: any) {
      console.error('❌ Failed to save security settings:', error);
      
      // Handle different error scenarios
      if (error.response?.status === 400) {
        return {
          success: false,
          message: error.response.data?.message || 'Invalid settings data provided'
        };
      } else if (error.response?.status === 401) {
        return {
          success: false,
          message: 'Unauthorized. Please log in again.'
        };
      } else if (error.response?.status === 403) {
        return {
          success: false,
          message: 'Insufficient permissions to update settings'
        };
      } else {
        return {
          success: false,
          message: 'Failed to save settings. Please try again.'
        };
      }
    }
  },

  // Get default settings
  getDefaultSettings(): SecuritySettings {
    return {
      twoFactorAuthEnabled: true,
      strongPasswordPolicyEnabled: true,
      sessionTimeoutMinutes: 30,
      maxLoginAttempts: 5,
      dataEncryptionEnabled: true,
      auditLoggingEnabled: true
    };
  },

  // Validate settings before sending to backend
  validateSettings(settings: SecuritySettings): SecuritySettings {
    const validated = { ...settings };
    
    // Validate session timeout (5-480 minutes)
    if (validated.sessionTimeoutMinutes < 5) {
      validated.sessionTimeoutMinutes = 5;
    } else if (validated.sessionTimeoutMinutes > 480) {
      validated.sessionTimeoutMinutes = 480;
    }
    
    // Validate login attempts (1-10)
    if (validated.maxLoginAttempts < 1) {
      validated.maxLoginAttempts = 1;
    } else if (validated.maxLoginAttempts > 10) {
      validated.maxLoginAttempts = 10;
    }
    
    return validated;
  },

  // Get settings audit trail
  async getSettingsAuditTrail(): Promise<any[]> {
    try {
      console.log('🚀 Fetching settings audit trail...');
      const response = await api.get('/admin/settings/audit-trail');
      console.log('✅ Settings audit trail fetched successfully:', response);
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error: any) {
      console.error('❌ Failed to fetch settings audit trail:', error);
      return [];
    }
  },

  // Get security metrics
  async getSecurityMetrics(): Promise<any> {
    try {
      console.log('🚀 Fetching security metrics...');
      const response = await api.get('/admin/settings/security-metrics');
      console.log('✅ Security metrics fetched successfully:', response);
      return response.data || response;
    } catch (error: any) {
      console.error('❌ Failed to fetch security metrics:', error);
      return {
        securityScore: 85,
        enabledFeatures: 5,
        totalFeatures: 6,
        lastUpdated: new Date().toISOString()
      };
    }
  },

  // Test settings configuration
  async testSettings(settings: SecuritySettings): Promise<{ valid: boolean; issues: string[] }> {
    try {
      console.log('🚀 Testing settings configuration...');
      const response = await api.post('/admin/settings/test', settings);
      console.log('✅ Settings test completed:', response);
      return response.data || response;
    } catch (error: any) {
      console.error('❌ Settings test failed:', error);
      return {
        valid: false,
        issues: ['Unable to test settings configuration']
      };
    }
  },

  // Reset settings to defaults
  async resetToDefaults(): Promise<SettingsResponse> {
    try {
      console.log('🚀 Resetting settings to defaults...');
      const defaultSettings = this.getDefaultSettings();
      const response = await this.saveSecuritySettings(defaultSettings);
      
      if (response.success) {
        return {
          success: true,
          message: 'Settings reset to defaults successfully',
          data: defaultSettings
        };
      } else {
        return response;
      }
    } catch (error: any) {
      console.error('❌ Failed to reset settings:', error);
      return {
        success: false,
        message: 'Failed to reset settings. Please try again.'
      };
    }
  },

  // Export settings configuration
  async exportSettings(): Promise<Blob> {
    try {
      console.log('🚀 Exporting settings configuration...');
      const settings = await this.getSecuritySettings();
      
      const exportData = {
        exportDate: new Date().toISOString(),
        version: '1.0',
        settings: settings
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      
      console.log('✅ Settings exported successfully');
      return blob;
    } catch (error: any) {
      console.error('❌ Failed to export settings:', error);
      throw new Error('Failed to export settings configuration');
    }
  },

  // Import settings configuration
  async importSettings(file: File): Promise<SettingsResponse> {
    try {
      console.log('🚀 Importing settings configuration...');
      
      const text = await file.text();
      const importData = JSON.parse(text);
      
      if (importData.settings) {
        const response = await this.saveSecuritySettings(importData.settings);
        return {
          success: response.success,
          message: response.success ? 'Settings imported successfully' : response.message,
          data: response.data
        };
      } else {
        return {
          success: false,
          message: 'Invalid settings file format'
        };
      }
    } catch (error: any) {
      console.error('❌ Failed to import settings:', error);
      return {
        success: false,
        message: 'Failed to import settings configuration'
      };
    }
  }
};
