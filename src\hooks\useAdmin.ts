import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminService } from '@/services/adminService';

// Query Keys
export const adminKeys = {
  all: ['admin'] as const,
  systemOverview: () => [...adminKeys.all, 'system-overview'] as const,
  users: () => [...adminKeys.all, 'users'] as const,
  user: (id: number) => [...adminKeys.all, 'user', id] as const,
  pendingDoctors: () => [...adminKeys.all, 'pending-doctors'] as const,
  pendingClinics: () => [...adminKeys.all, 'pending-clinics'] as const,
  auditLogs: (page: number) => [...adminKeys.all, 'audit-logs', page] as const,
  analytics: (type: string, period: string) => [...adminKeys.all, 'analytics', type, period] as const,
  settings: () => [...adminKeys.all, 'settings'] as const,
};

// System Overview Hook
export const useSystemOverview = () => {
  return useQuery({
    queryKey: adminKeys.systemOverview(),
    queryFn: () => adminService.getSystemOverview(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: false,
  });
};

// User Management Hooks
export const useAllUsers = () => {
  return useQuery({
    queryKey: adminKeys.users(),
    queryFn: () => adminService.getAllUsers(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });
};

export const useUser = (id: number) => {
  return useQuery({
    queryKey: adminKeys.user(id),
    queryFn: () => adminService.getUserById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateUserStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: number; status: string }) =>
      adminService.updateUserStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.invalidateQueries({ queryKey: adminKeys.pendingDoctors() });
      queryClient.invalidateQueries({ queryKey: adminKeys.pendingClinics() });
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => adminService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
    },
  });
};

export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userData: {
      name: string;
      email: string;
      password: string;
      phoneNumber?: string;
      address?: string;
      dateOfBirth?: string;
      gender: 'MALE' | 'FEMALE' | 'OTHER';
      role: 'PATIENT' | 'DOCTOR' | 'CLINIC' | 'CLINIC_STAFF' | 'ADMIN';
    }) => adminService.createUser(userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.refetchQueries({ queryKey: adminKeys.users() });
    },
  });
};

export const useUpdateUserRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, role, reason }: { id: number; role: string; reason?: string }) =>
      adminService.updateUserRole(id, role, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.refetchQueries({ queryKey: adminKeys.users() });
    },
  });
};

export const useActivateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => adminService.activateUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.refetchQueries({ queryKey: adminKeys.users() });
    },
  });
};

export const useDeactivateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => adminService.deactivateUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.refetchQueries({ queryKey: adminKeys.users() });
    },
  });
};

// Doctor Approval Hooks
export const usePendingDoctors = () => {
  return useQuery({
    queryKey: adminKeys.pendingDoctors(),
    queryFn: () => adminService.getPendingDoctors(),
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: false,
  });
};

export const useApproveDoctor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => adminService.approveDoctor(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.pendingDoctors() });
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.invalidateQueries({ queryKey: adminKeys.systemOverview() });
    },
  });
};

export const useRejectDoctor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => adminService.rejectDoctor(id),
    onSuccess: () => {
      // Force refetch of all related queries
      queryClient.invalidateQueries({ queryKey: adminKeys.pendingDoctors() });
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.invalidateQueries({ queryKey: adminKeys.systemOverview() });

      // Force immediate refetch
      queryClient.refetchQueries({ queryKey: adminKeys.users() });
      queryClient.refetchQueries({ queryKey: adminKeys.pendingDoctors() });
    },
  });
};

// Clinic Approval Hooks
export const usePendingClinics = () => {
  return useQuery({
    queryKey: adminKeys.pendingClinics(),
    queryFn: () => adminService.getPendingClinics(),
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: false,
  });
};

export const useApproveClinic = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => adminService.approveClinic(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.pendingClinics() });
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.invalidateQueries({ queryKey: adminKeys.systemOverview() });
    },
  });
};

export const useRejectClinic = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => adminService.rejectClinic(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.pendingClinics() });
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.invalidateQueries({ queryKey: adminKeys.systemOverview() });
    },
  });
};

// Analytics Hooks
export const useUserAnalytics = (period: string = '30d') => {
  return useQuery({
    queryKey: adminKeys.analytics('users', period),
    queryFn: () => adminService.getUserAnalytics(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: false,
  });
};

export const useAppointmentAnalytics = (period: string = '30d') => {
  return useQuery({
    queryKey: adminKeys.analytics('appointments', period),
    queryFn: () => adminService.getAppointmentAnalytics(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: false,
  });
};

// Audit Logs Hook
export const useAuditLogs = (page: number = 1, limit: number = 50) => {
  return useQuery({
    queryKey: adminKeys.auditLogs(page),
    queryFn: () => adminService.getAuditLogs(page, limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });
};

// System Health Hook
export const useSystemHealth = () => {
  return useQuery({
    queryKey: [...adminKeys.all, 'system-health'],
    queryFn: () => adminService.getSystemHealth(),
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    retry: false,
  });
};

// Settings Hooks
export const useSystemSettings = () => {
  return useQuery({
    queryKey: adminKeys.settings(),
    queryFn: () => adminService.getSystemSettings(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: false,
  });
};

export const useUpdateSystemSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: {
      twoFactorAuthEnabled: boolean;
      strongPasswordPolicyEnabled: boolean;
      sessionTimeoutMinutes: number;
      maxLoginAttempts: number;
      dataEncryptionEnabled: boolean;
      auditLoggingEnabled: boolean;
    }) => adminService.updateSystemSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: adminKeys.settings() });
      queryClient.refetchQueries({ queryKey: adminKeys.settings() });
    },
  });
};
