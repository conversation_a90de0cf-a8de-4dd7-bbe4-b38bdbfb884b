import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { doctorAppointmentsService, DatabaseAppointment, AppointmentStatistics } from '@/services/doctorAppointmentsService';
import { useToast } from '@/hooks/use-toast';

// ✅ Database Query Keys for Cache Management
export const appointmentQueryKeys = {
  all: ['appointments'] as const,
  doctor: (doctorId: number) => [...appointmentQueryKeys.all, 'doctor', doctorId] as const,
  todayAppointments: (doctorId: number) => [...appointmentQueryKeys.doctor(doctorId), 'today'] as const,
  upcomingAppointments: (doctorId: number) => [...appointmentQueryKeys.doctor(doctorId), 'upcoming'] as const,
  completedAppointments: (doctorId: number) => [...appointmentQueryKeys.doctor(doctorId), 'completed'] as const,
  allAppointments: (doctorId: number) => [...appointmentQueryKeys.doctor(doctorId), 'all'] as const,
  appointmentDetails: (appointmentId: number) => [...appointmentQueryKeys.all, 'details', appointmentId] as const,
  patientAppointments: (patientId: number) => [...appointmentQueryKeys.all, 'patient', patientId] as const,
  appointmentStatistics: (doctorId: number) => [...appointmentQueryKeys.doctor(doctorId), 'statistics'] as const,
  availableSlots: (doctorId: number, date: string) => [...appointmentQueryKeys.doctor(doctorId), 'slots', date] as const,
};

// ✅ Database Query Hooks

// Get today's appointments from database
export const useTodayAppointments = (doctorId: number) => {
  return useQuery({
    queryKey: appointmentQueryKeys.todayAppointments(doctorId),
    queryFn: () => doctorAppointmentsService.getTodayAppointments(doctorId),
    enabled: !!doctorId,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes for real-time updates
  });
};

// Get upcoming appointments from database
export const useUpcomingAppointments = (doctorId: number) => {
  return useQuery({
    queryKey: appointmentQueryKeys.upcomingAppointments(doctorId),
    queryFn: () => doctorAppointmentsService.getUpcomingAppointments(doctorId),
    enabled: !!doctorId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};

// Get completed appointments from database
export const useCompletedAppointments = (doctorId: number) => {
  return useQuery({
    queryKey: appointmentQueryKeys.completedAppointments(doctorId),
    queryFn: () => doctorAppointmentsService.getCompletedAppointments(doctorId),
    enabled: !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get all appointments from database
export const useAllAppointments = (doctorId: number, status?: string, date?: string) => {
  return useQuery({
    queryKey: [...appointmentQueryKeys.allAppointments(doctorId), status, date],
    queryFn: () => doctorAppointmentsService.getAllAppointments(doctorId, status, date),
    enabled: !!doctorId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get appointment details from database
export const useAppointmentDetails = (appointmentId: number) => {
  return useQuery({
    queryKey: appointmentQueryKeys.appointmentDetails(appointmentId),
    queryFn: () => doctorAppointmentsService.getAppointmentById(appointmentId),
    enabled: !!appointmentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get patient's appointments from database
export const usePatientAppointments = (patientId: number) => {
  return useQuery({
    queryKey: appointmentQueryKeys.patientAppointments(patientId),
    queryFn: () => doctorAppointmentsService.getPatientAppointments(patientId),
    enabled: !!patientId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get appointment statistics from database (optional endpoint)
export const useAppointmentStatistics = (doctorId: number) => {
  return useQuery({
    queryKey: appointmentQueryKeys.appointmentStatistics(doctorId),
    queryFn: () => doctorAppointmentsService.getAppointmentStatistics(doctorId),
    enabled: !!doctorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false, // Don't retry if endpoint doesn't exist
    refetchOnWindowFocus: false, // Don't refetch on focus if endpoint is not available
  });
};

// Get available time slots from database
export const useAvailableSlots = (doctorId: number, date: string) => {
  return useQuery({
    queryKey: appointmentQueryKeys.availableSlots(doctorId, date),
    queryFn: () => doctorAppointmentsService.getAvailableSlots(doctorId, date),
    enabled: !!doctorId && !!date,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// ✅ Database Mutation Hooks

// Update appointment status in database
export const useUpdateAppointmentStatus = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ appointmentId, status }: { appointmentId: number; status: string }) =>
      doctorAppointmentsService.updateAppointmentStatus(appointmentId, status),
    onSuccess: (result, { appointmentId }) => {
      // Invalidate all appointment-related queries for real-time updates
      queryClient.invalidateQueries({ queryKey: appointmentQueryKeys.all });
      
      toast({
        title: "Status Updated",
        description: result.message,
      });

      console.log('✅ Appointment status updated successfully');
    },
    onError: (error: any) => {
      console.error('❌ Appointment status update error:', error);
      
      const errorMessage = error.message || error.toString();
      let title = "Update Failed";
      let description = "Failed to update appointment status.";
      
      if (errorMessage.includes("not found")) {
        description = "Appointment not found. It may have been deleted.";
      } else if (errorMessage.includes("invalid status")) {
        description = "Invalid status provided. Please try again.";
      }
      
      toast({
        title,
        description,
        variant: "destructive",
      });
    },
  });
};

// Update appointment notes in database
export const useUpdateAppointmentNotes = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ appointmentId, notes }: { appointmentId: number; notes: string }) =>
      doctorAppointmentsService.updateAppointmentNotes(appointmentId, notes),
    onSuccess: (result, { appointmentId }) => {
      // Invalidate specific appointment and related queries
      queryClient.invalidateQueries({ queryKey: appointmentQueryKeys.appointmentDetails(appointmentId) });
      queryClient.invalidateQueries({ queryKey: appointmentQueryKeys.all });
      
      toast({
        title: "Notes Updated",
        description: result.message,
      });

      console.log('✅ Appointment notes updated successfully');
    },
    onError: (error: any) => {
      console.error('❌ Appointment notes update error:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update appointment notes. Please try again.",
        variant: "destructive",
      });
    },
  });
};

// Reschedule appointment in database
export const useRescheduleAppointment = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ appointmentId, appointmentDate, notes }: { 
      appointmentId: number; 
      appointmentDate: string; 
      notes?: string; 
    }) => doctorAppointmentsService.rescheduleAppointment(appointmentId, appointmentDate, notes),
    onSuccess: (result, { appointmentId }) => {
      // Invalidate all appointment queries for real-time updates
      queryClient.invalidateQueries({ queryKey: appointmentQueryKeys.all });
      
      toast({
        title: "Appointment Rescheduled",
        description: result.message,
      });

      console.log('✅ Appointment rescheduled successfully');
    },
    onError: (error: any) => {
      console.error('❌ Appointment reschedule error:', error);
      
      const errorMessage = error.message || error.toString();
      let description = "Failed to reschedule appointment. Please try again.";
      
      if (errorMessage.includes("slot not available")) {
        description = "The selected time slot is not available. Please choose a different time.";
      } else if (errorMessage.includes("past date")) {
        description = "Cannot reschedule to a past date. Please select a future date.";
      }
      
      toast({
        title: "Reschedule Failed",
        description,
        variant: "destructive",
      });
    },
  });
};

// Cancel appointment in database
export const useCancelAppointment = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ appointmentId, reason }: { appointmentId: number; reason: string }) =>
      doctorAppointmentsService.cancelAppointment(appointmentId, reason),
    onSuccess: (result, { appointmentId }) => {
      // Invalidate all appointment queries
      queryClient.invalidateQueries({ queryKey: appointmentQueryKeys.all });
      
      toast({
        title: "Appointment Cancelled",
        description: result.message,
      });

      console.log('✅ Appointment cancelled successfully');
    },
    onError: (error: any) => {
      console.error('❌ Appointment cancellation error:', error);
      toast({
        title: "Cancellation Failed",
        description: "Failed to cancel appointment. Please try again.",
        variant: "destructive",
      });
    },
  });
};

// Complete appointment with diagnosis in database
export const useCompleteAppointmentWithDiagnosis = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ appointmentId, diagnosisData }: { 
      appointmentId: number; 
      diagnosisData: {
        diagnosisCode: string;
        diagnosisDescription: string;
        symptoms: string;
        treatmentPlan: string;
        notes?: string;
      };
    }) => doctorAppointmentsService.completeAppointmentWithDiagnosis(appointmentId, diagnosisData),
    onSuccess: (result, { appointmentId }) => {
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: appointmentQueryKeys.all });
      
      toast({
        title: "Appointment Completed",
        description: result.message,
      });

      console.log('✅ Appointment completed with diagnosis successfully');
    },
    onError: (error: any) => {
      console.error('❌ Appointment completion error:', error);
      toast({
        title: "Completion Failed",
        description: "Failed to complete appointment with diagnosis. Please try again.",
        variant: "destructive",
      });
    },
  });
};

// ✅ Combined hook for all appointment data
export const useDoctorAppointmentsData = (doctorId: number) => {
  const todayQuery = useTodayAppointments(doctorId);
  const upcomingQuery = useUpcomingAppointments(doctorId);
  const completedQuery = useCompletedAppointments(doctorId);
  // Statistics query is optional - don't include in main loading/error states
  const statisticsQuery = useAppointmentStatistics(doctorId);

  const isLoading = todayQuery.isLoading || upcomingQuery.isLoading || completedQuery.isLoading;
  const isError = todayQuery.isError || upcomingQuery.isError || completedQuery.isError;
  const error = todayQuery.error || upcomingQuery.error || completedQuery.error;

  return {
    // Data from database
    todayAppointments: todayQuery.data || [],
    upcomingAppointments: upcomingQuery.data || [],
    completedAppointments: completedQuery.data || [],
    statistics: statisticsQuery.data,

    // Loading states
    isLoading,
    isError,
    error,

    // Individual loading states
    todayLoading: todayQuery.isLoading,
    upcomingLoading: upcomingQuery.isLoading,
    completedLoading: completedQuery.isLoading,
    statisticsLoading: statisticsQuery.isLoading,

    // Refetch functions for real-time updates
    refetchToday: todayQuery.refetch,
    refetchUpcoming: upcomingQuery.refetch,
    refetchCompleted: completedQuery.refetch,
    refetchStatistics: statisticsQuery.refetch,

    // Refetch all data
    refetchAll: () => {
      todayQuery.refetch();
      upcomingQuery.refetch();
      completedQuery.refetch();
      statisticsQuery.refetch();
    }
  };
};
