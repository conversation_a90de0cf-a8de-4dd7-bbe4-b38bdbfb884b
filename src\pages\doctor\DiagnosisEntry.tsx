
import { useState } from "react";
import { useParams } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, User, Calendar } from "lucide-react";

const DiagnosisEntry = () => {
  const { patientId } = useParams();
  const [diagnosisCode, setDiagnosisCode] = useState("");
  const [diagnosis, setDiagnosis] = useState("");
  const [symptoms, setSymptoms] = useState("");
  const [treatment, setTreatment] = useState("");
  const [notes, setNotes] = useState("");

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Diagnosis Entry</h1>
          <p className="text-gray-600">Record diagnosis and treatment plan for patient #{patientId}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Medical Diagnosis
              </CardTitle>
              <CardDescription>Enter the diagnosis details and treatment plan</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="diagnosisCode">Diagnosis Code</Label>
                  <Input 
                    id="diagnosisCode"
                    placeholder="ICD-10 Code (e.g., I10)"
                    value={diagnosisCode}
                    onChange={(e) => setDiagnosisCode(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="severity">Severity</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select severity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mild">Mild</SelectItem>
                      <SelectItem value="moderate">Moderate</SelectItem>
                      <SelectItem value="severe">Severe</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="diagnosis">Diagnosis Description</Label>
                <Input 
                  id="diagnosis"
                  placeholder="Primary diagnosis"
                  value={diagnosis}
                  onChange={(e) => setDiagnosis(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="symptoms">Symptoms</Label>
                <Textarea 
                  id="symptoms"
                  placeholder="List observed symptoms..."
                  value={symptoms}
                  onChange={(e) => setSymptoms(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="treatment">Treatment Plan</Label>
                <Textarea 
                  id="treatment"
                  placeholder="Recommended treatment and medications..."
                  value={treatment}
                  onChange={(e) => setTreatment(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Doctor's Notes</Label>
                <Textarea 
                  id="notes"
                  placeholder="Additional observations and recommendations..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                />
              </div>

              <div className="flex space-x-4">
                <Button className="flex-1">Save Diagnosis</Button>
                <Button variant="outline" className="flex-1">Save as Draft</Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Patient Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Patient ID</Label>
                <p className="text-sm text-gray-600">#{patientId}</p>
              </div>
              <div className="space-y-2">
                <Label>Name</Label>
                <p className="text-sm text-gray-600">John Smith</p>
              </div>
              <div className="space-y-2">
                <Label>Age</Label>
                <p className="text-sm text-gray-600">45 years</p>
              </div>
              <div className="space-y-2">
                <Label>Visit Date</Label>
                <p className="text-sm text-gray-600">{new Date().toLocaleDateString()}</p>
              </div>
              <div className="space-y-2">
                <Label>Previous Conditions</Label>
                <p className="text-sm text-gray-600">Hypertension, Diabetes</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DiagnosisEntry;
