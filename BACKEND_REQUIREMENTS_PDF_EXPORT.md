# 🔧 BACKEND REQUIREMENTS FOR PDF EXPORT FUNCTIONALITY

## 🎯 **REQUIRED ENDPOINTS FOR PDF EXPORT**

The PDF export functionality needs these backend endpoints to fetch real data for report generation:

### **📊 REPORTS DATA ENDPOINTS**

#### **1. System Statistics Endpoint**
```java
@RestController
@RequestMapping("/api/admin/reports")
@PreAuthorize("hasRole('ADMIN')")
public class ReportsController {

    @GetMapping("/system-stats")
    public ResponseEntity<SystemStatsResponse> getSystemStats(
        @RequestParam(defaultValue = "30") int days) {
        
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        
        SystemStatsResponse stats = new SystemStatsResponse();
        
        // User statistics
        stats.setTotalUsers(userService.getTotalUsers());
        stats.setActiveUsers(userService.getActiveUsersCount());
        stats.setPendingUsers(userService.getPendingUsersCount());
        stats.setInactiveUsers(userService.getInactiveUsersCount());
        
        // Role distribution
        stats.setDoctorCount(userService.getUserCountByRole(Role.DOCTOR));
        stats.setPatientCount(userService.getUserCountByRole(Role.PATIENT));
        stats.setClinicCount(userService.getUserCountByRole(Role.CLINIC));
        stats.setClinicStaffCount(userService.getUserCountByRole(Role.CLINIC_STAFF));
        stats.setAdminCount(userService.getUserCountByRole(Role.ADMIN));
        
        // Appointment statistics (if you have appointments)
        stats.setTotalAppointments(appointmentService.getTotalAppointments());
        stats.setCompletedAppointments(appointmentService.getCompletedAppointments());
        stats.setPendingAppointments(appointmentService.getPendingAppointments());
        stats.setCancelledAppointments(appointmentService.getCancelledAppointments());
        
        // System metrics
        stats.setSystemUptime("99.9%"); // Calculate actual uptime
        stats.setApiResponseTime("245ms"); // Calculate from monitoring
        stats.setDatabaseQueryTime("89ms"); // Calculate from monitoring
        stats.setErrorRate("0.02%"); // Calculate from logs
        stats.setActiveSessions(sessionService.getActiveSessionsCount());
        
        return ResponseEntity.ok(stats);
    }
}

// Response DTO
public class SystemStatsResponse {
    // User statistics
    private Long totalUsers;
    private Long activeUsers;
    private Long pendingUsers;
    private Long inactiveUsers;
    
    // Role distribution
    private Long doctorCount;
    private Long patientCount;
    private Long clinicCount;
    private Long clinicStaffCount;
    private Long adminCount;
    
    // Appointment statistics
    private Long totalAppointments;
    private Long completedAppointments;
    private Long pendingAppointments;
    private Long cancelledAppointments;
    
    // System metrics
    private String systemUptime;
    private String apiResponseTime;
    private String databaseQueryTime;
    private String errorRate;
    private Long activeSessions;
    
    // getters and setters
}
```

#### **2. User Growth Data Endpoint**
```java
@GetMapping("/user-growth")
public ResponseEntity<List<UserGrowthData>> getUserGrowthData(
    @RequestParam(defaultValue = "12") int months) {
    
    List<UserGrowthData> growthData = new ArrayList<>();
    LocalDateTime startDate = LocalDateTime.now().minusMonths(months);
    
    for (int i = 0; i < months; i++) {
        LocalDateTime monthStart = startDate.plusMonths(i);
        LocalDateTime monthEnd = monthStart.plusMonths(1);
        
        UserGrowthData monthData = new UserGrowthData();
        monthData.setMonth(monthStart.format(DateTimeFormatter.ofPattern("MMM yyyy")));
        monthData.setPatients(userService.getUsersCreatedInPeriod(Role.PATIENT, monthStart, monthEnd));
        monthData.setDoctors(userService.getUsersCreatedInPeriod(Role.DOCTOR, monthStart, monthEnd));
        monthData.setClinics(userService.getUsersCreatedInPeriod(Role.CLINIC, monthStart, monthEnd));
        monthData.setClinicStaff(userService.getUsersCreatedInPeriod(Role.CLINIC_STAFF, monthStart, monthEnd));
        
        growthData.add(monthData);
    }
    
    return ResponseEntity.ok(growthData);
}

// Response DTO
public class UserGrowthData {
    private String month;
    private Long patients;
    private Long doctors;
    private Long clinics;
    private Long clinicStaff;
    
    // getters and setters
}
```

#### **3. Appointment Analytics Endpoint**
```java
@GetMapping("/appointment-analytics")
public ResponseEntity<List<AppointmentAnalytics>> getAppointmentAnalytics(
    @RequestParam(defaultValue = "30") int days) {
    
    LocalDateTime startDate = LocalDateTime.now().minusDays(days);
    
    List<AppointmentAnalytics> analytics = new ArrayList<>();
    
    // Get appointment counts by status
    analytics.add(new AppointmentAnalytics("Completed", 
        appointmentService.getAppointmentCountByStatus(AppointmentStatus.COMPLETED, startDate)));
    analytics.add(new AppointmentAnalytics("Pending", 
        appointmentService.getAppointmentCountByStatus(AppointmentStatus.PENDING, startDate)));
    analytics.add(new AppointmentAnalytics("Cancelled", 
        appointmentService.getAppointmentCountByStatus(AppointmentStatus.CANCELLED, startDate)));
    analytics.add(new AppointmentAnalytics("No Show", 
        appointmentService.getAppointmentCountByStatus(AppointmentStatus.NO_SHOW, startDate)));
    
    return ResponseEntity.ok(analytics);
}

// Response DTO
public class AppointmentAnalytics {
    private String status;
    private Long count;
    
    public AppointmentAnalytics(String status, Long count) {
        this.status = status;
        this.count = count;
    }
    
    // getters and setters
}
```

#### **4. Performance Metrics Endpoint**
```java
@GetMapping("/performance-metrics")
public ResponseEntity<PerformanceMetrics> getPerformanceMetrics() {
    PerformanceMetrics metrics = new PerformanceMetrics();
    
    // Calculate actual metrics from your monitoring system
    metrics.setApiResponseTime(monitoringService.getAverageApiResponseTime());
    metrics.setDatabaseQueryTime(monitoringService.getAverageDatabaseQueryTime());
    metrics.setErrorRate(monitoringService.getErrorRate());
    metrics.setActiveSessions(sessionService.getActiveSessionsCount());
    metrics.setSystemUptime(monitoringService.getSystemUptime());
    metrics.setCpuUsage(monitoringService.getCpuUsage());
    metrics.setMemoryUsage(monitoringService.getMemoryUsage());
    metrics.setDiskUsage(monitoringService.getDiskUsage());
    
    return ResponseEntity.ok(metrics);
}

// Response DTO
public class PerformanceMetrics {
    private String apiResponseTime;
    private String databaseQueryTime;
    private String errorRate;
    private Long activeSessions;
    private String systemUptime;
    private String cpuUsage;
    private String memoryUsage;
    private String diskUsage;
    
    // getters and setters
}
```

#### **5. Revenue Analytics Endpoint (Optional)**
```java
@GetMapping("/revenue-analytics")
public ResponseEntity<RevenueAnalytics> getRevenueAnalytics(
    @RequestParam(defaultValue = "30") int days) {
    
    LocalDateTime startDate = LocalDateTime.now().minusDays(days);
    
    RevenueAnalytics analytics = new RevenueAnalytics();
    
    // Calculate revenue metrics (if applicable to your system)
    analytics.setMonthlyRecurringRevenue(revenueService.getMonthlyRecurringRevenue());
    analytics.setTotalSubscriptionRevenue(revenueService.getTotalSubscriptionRevenue(startDate));
    analytics.setAverageRevenuePerUser(revenueService.getAverageRevenuePerUser());
    analytics.setChurnRate(revenueService.getChurnRate());
    analytics.setNewSubscriptions(revenueService.getNewSubscriptions(startDate));
    analytics.setCancelledSubscriptions(revenueService.getCancelledSubscriptions(startDate));
    
    return ResponseEntity.ok(analytics);
}

// Response DTO
public class RevenueAnalytics {
    private BigDecimal monthlyRecurringRevenue;
    private BigDecimal totalSubscriptionRevenue;
    private BigDecimal averageRevenuePerUser;
    private Double churnRate;
    private Long newSubscriptions;
    private Long cancelledSubscriptions;
    
    // getters and setters
}
```

## 🔧 **REQUIRED SERVICE METHODS**

### **UserService Enhancements**
```java
@Service
public class UserService {
    
    public Long getTotalUsers() {
        return userRepository.count();
    }
    
    public Long getActiveUsersCount() {
        return userRepository.countByStatus(UserStatus.ACTIVE);
    }
    
    public Long getPendingUsersCount() {
        return userRepository.countByStatus(UserStatus.PENDING_APPROVAL);
    }
    
    public Long getInactiveUsersCount() {
        return userRepository.countByStatus(UserStatus.INACTIVE);
    }
    
    public Long getUserCountByRole(Role role) {
        return userRepository.countByRole(role);
    }
    
    public Long getUsersCreatedInPeriod(Role role, LocalDateTime start, LocalDateTime end) {
        return userRepository.countByRoleAndCreatedAtBetween(role, start, end);
    }
    
    public List<User> getUsersCreatedAfter(LocalDateTime date) {
        return userRepository.findByCreatedAtAfter(date);
    }
    
    public Map<String, Long> getUserRegistrationsByMonth(int months) {
        LocalDateTime startDate = LocalDateTime.now().minusMonths(months);
        List<User> users = userRepository.findByCreatedAtAfter(startDate);
        
        return users.stream()
            .collect(Collectors.groupingBy(
                user -> user.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM yyyy")),
                Collectors.counting()
            ));
    }
}
```

### **Repository Enhancements**
```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    Long countByStatus(UserStatus status);
    Long countByRole(Role role);
    Long countByRoleAndStatus(Role role, UserStatus status);
    Long countByRoleAndCreatedAtBetween(Role role, LocalDateTime start, LocalDateTime end);
    
    List<User> findByCreatedAtAfter(LocalDateTime date);
    List<User> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);
    List<User> findByRoleAndCreatedAtBetween(Role role, LocalDateTime start, LocalDateTime end);
    
    @Query("SELECT u.role, COUNT(u) FROM User u GROUP BY u.role")
    List<Object[]> countUsersByRole();
    
    @Query("SELECT u.status, COUNT(u) FROM User u GROUP BY u.status")
    List<Object[]> countUsersByStatus();
    
    @Query("SELECT DATE_FORMAT(u.createdAt, '%Y-%m'), COUNT(u) FROM User u " +
           "WHERE u.createdAt >= :startDate GROUP BY DATE_FORMAT(u.createdAt, '%Y-%m')")
    List<Object[]> getUserRegistrationsByMonth(@Param("startDate") LocalDateTime startDate);
}
```

## 📊 **OPTIONAL: APPOINTMENT SYSTEM SUPPORT**

### **If You Have Appointments**
```java
@Entity
public class Appointment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    private User patient;
    
    @ManyToOne
    private User doctor;
    
    private LocalDateTime appointmentDate;
    
    @Enumerated(EnumType.STRING)
    private AppointmentStatus status;
    
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // getters and setters
}

public enum AppointmentStatus {
    PENDING, CONFIRMED, COMPLETED, CANCELLED, NO_SHOW
}

@Service
public class AppointmentService {
    
    public Long getTotalAppointments() {
        return appointmentRepository.count();
    }
    
    public Long getCompletedAppointments() {
        return appointmentRepository.countByStatus(AppointmentStatus.COMPLETED);
    }
    
    public Long getPendingAppointments() {
        return appointmentRepository.countByStatus(AppointmentStatus.PENDING);
    }
    
    public Long getCancelledAppointments() {
        return appointmentRepository.countByStatus(AppointmentStatus.CANCELLED);
    }
    
    public Long getAppointmentCountByStatus(AppointmentStatus status, LocalDateTime since) {
        return appointmentRepository.countByStatusAndCreatedAtAfter(status, since);
    }
}
```

## 🔒 **SECURITY CONFIGURATION**

### **Ensure Admin Access**
```java
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.authorizeHttpRequests(authz -> authz
            .requestMatchers("/api/admin/reports/**").hasRole("ADMIN")
            .requestMatchers("/api/admin/**").hasRole("ADMIN")
            // ... other configurations
        );
        
        return http.build();
    }
}
```

## 🎯 **MINIMAL IMPLEMENTATION**

### **If You Want to Start Simple**
```java
// Just implement these 2 endpoints first:
@GetMapping("/api/admin/reports/system-stats")
@GetMapping("/api/admin/users") // Already exists

// The frontend will use:
// 1. System stats for system reports
// 2. User list for user reports
// 3. Mock data for other metrics until you implement them
```

## 📋 **IMPLEMENTATION CHECKLIST**

### **Priority 1 (Essential)**
- [ ] **GET /api/admin/reports/system-stats** - Basic system statistics
- [ ] **Enhance existing user endpoints** - Already working
- [ ] **Add user count methods** - Role and status counts

### **Priority 2 (Enhanced Reports)**
- [ ] **GET /api/admin/reports/user-growth** - Monthly user registration data
- [ ] **GET /api/admin/reports/performance-metrics** - System performance data
- [ ] **Add repository query methods** - For statistics calculations

### **Priority 3 (Complete Analytics)**
- [ ] **GET /api/admin/reports/appointment-analytics** - If you have appointments
- [ ] **GET /api/admin/reports/revenue-analytics** - If you have revenue tracking
- [ ] **Monitoring integration** - For real performance metrics

## 🚀 **TESTING YOUR IMPLEMENTATION**

### **Test Endpoints**
```bash
# Test system stats
curl -X GET http://localhost:8083/api/admin/reports/system-stats \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test user growth
curl -X GET http://localhost:8083/api/admin/reports/user-growth \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test with date range
curl -X GET "http://localhost:8083/api/admin/reports/system-stats?days=7" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### **Expected Response Format**
```json
{
  "totalUsers": 150,
  "activeUsers": 120,
  "pendingUsers": 25,
  "inactiveUsers": 5,
  "doctorCount": 30,
  "patientCount": 100,
  "clinicCount": 15,
  "clinicStaffCount": 5,
  "totalAppointments": 500,
  "systemUptime": "99.9%",
  "apiResponseTime": "245ms",
  "activeSessions": 45
}
```

## 🎉 **RESULT**

**Once you implement these endpoints:**

✅ **PDF Export will use real data** from your database
✅ **System reports will show actual statistics** 
✅ **User reports will include real user data**
✅ **Performance metrics will reflect your system**
✅ **All reports will be dynamic and current**

**The frontend is already configured to use these endpoints and will automatically switch from mock data to real data once your backend provides them!** 🚀
