
import { api } from './api';
import { Patient, PatientUpdateRequest, AppointmentRequest, AppointmentResponse, DoctorProfile } from '@/types/api';

export const patientService = {
  // Patient Profile
  async getProfile(patientId: number): Promise<Patient> {
    return await api.get('/patients/profile', { patientId });
  },

  async updateProfile(patientId: number, profileData: PatientUpdateRequest): Promise<Patient> {
    return await api.put('/patients/profile', profileData, { patientId });
  },

  // Appointments
  async createAppointment(appointmentData: AppointmentRequest): Promise<AppointmentResponse> {
    return await api.post('/appointments', appointmentData);
  },

  async getAppointments(patientId: number, status?: string): Promise<AppointmentResponse[]> {
    const params: any = {};
    if (status) params.status = status;
    return await api.get(`/appointments/patient/${patientId}`, params);
  },

  async getAppointmentById(appointmentId: number): Promise<AppointmentResponse> {
    return await api.get(`/appointments/${appointmentId}`);
  },

  async cancelAppointment(appointmentId: number): Promise<void> {
    return await api.put(`/appointments/${appointmentId}/cancel`);
  },

  async rescheduleAppointment(appointmentId: number, newDate: string): Promise<void> {
    return await api.put(`/appointments/${appointmentId}/reschedule`, { appointmentDate: newDate });
  },

  // Doctor Search and Availability
  async searchDoctors(specialty?: string, location?: string, name?: string): Promise<DoctorProfile[]> {
    try {
      const params: any = {};
      if (specialty && specialty !== 'all') params.specialty = specialty;
      if (location) params.location = location;
      if (name) params.name = name;
      return await api.get('/patients/search/doctors', params);
    } catch (error) {
      console.warn('Doctor search API not available, using fallback');
      // Return mock data as fallback
      return [
        {
          id: 1,
          name: "Dr. Sarah Smith",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          address: "123 Medical Center Dr",
          dateOfBirth: "1980-01-01",
          gender: "FEMALE",
          medicalLicense: "MD123456",
          specialtyName: "Cardiology",
          yearsOfExperience: 10,
          qualification: "MD, Cardiology",
          doctorStatus: "ACTIVE",
          clinicId: 1,
          clinicName: "Downtown Medical Center",
          consultationFee: 150,
          bio: "Experienced cardiologist specializing in heart disease prevention and treatment.",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z"
        },
        {
          id: 2,
          name: "Dr. Michael Johnson",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          address: "456 Health Plaza",
          dateOfBirth: "1975-05-15",
          gender: "MALE",
          medicalLicense: "MD789012",
          specialtyName: "General Practice",
          yearsOfExperience: 15,
          qualification: "MD, Family Medicine",
          doctorStatus: "ACTIVE",
          clinicId: 2,
          clinicName: "City General Hospital",
          consultationFee: 100,
          bio: "Family medicine physician providing comprehensive healthcare for all ages.",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z"
        }
      ].filter(doctor => {
        if (specialty && specialty !== 'all' && !doctor.specialtyName.toLowerCase().includes(specialty.toLowerCase())) return false;
        if (name && !doctor.name.toLowerCase().includes(name.toLowerCase())) return false;
        return true;
      });
    }
  },

  async getDoctorAvailability(doctorId: number, date: string): Promise<string[]> {
    return await api.get(`/patients/doctors/${doctorId}/availability`, { date });
  },

  async getDoctorProfile(doctorId: number): Promise<DoctorProfile> {
    return await api.get(`/patients/doctors/${doctorId}/profile`);
  },

  // Medical History
  async getMedicalHistory(patientId: number): Promise<any> {
    return await api.get('/patients/medical-history', { patientId });
  },

  async getDiagnoses(patientId: number): Promise<any[]> {
    return await api.get('/patients/diagnoses', { patientId });
  },

  async getPrescriptions(patientId: number): Promise<any[]> {
    return await api.get('/patients/prescriptions', { patientId });
  },

  async getTestResults(patientId: number): Promise<any[]> {
    return await api.get('/patients/test-results', { patientId });
  },

  // Health Records
  async addHealthMetric(patientId: number, metricData: any): Promise<void> {
    return await api.post('/patients/health-metrics', metricData, { patientId });
  },

  async getHealthMetrics(patientId: number, type?: string): Promise<any[]> {
    const params: any = { patientId };
    if (type) params.type = type;
    return await api.get('/patients/health-metrics', params);
  },

  // Dashboard Stats
  async getDashboardStats(patientId: number): Promise<any> {
    try {
      return await api.get('/patients/dashboard/stats', { patientId });
    } catch (error) {
      console.warn('Dashboard stats API not available, using fallback');
      // Return mock stats as fallback
      return {
        totalAppointments: 12,
        upcomingAppointments: 2,
        completedAppointments: 10,
        activePrescriptions: 3,
        healthScore: "95%",
        lastCheckup: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
      };
    }
  }
};
