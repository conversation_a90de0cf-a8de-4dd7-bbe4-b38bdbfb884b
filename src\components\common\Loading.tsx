
import { Heart } from "lucide-react";

const Loading = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-white">
      <div className="text-center">
        <div className="relative">
          <Heart className="h-16 w-16 text-blue-600 mx-auto animate-pulse" />
          <div className="absolute inset-0 h-16 w-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
        </div>
        <h2 className="mt-4 text-xl font-semibold text-gray-700">Loading MediConnect...</h2>
        <p className="mt-2 text-gray-500">Please wait while we prepare your healthcare dashboard</p>
      </div>
    </div>
  );
};

export default Loading;
