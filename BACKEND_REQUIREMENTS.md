# 🔧 BACKEND REQUIREMENTS - COMPLETE SPRING BOOT IMPLEMENTATION

## 🎯 **REQUIRED ENDPOINTS FOR FRONTEND INTEGRATION**

Based on the frontend implementation, here are the exact endpoints your Spring Boot backend needs:

### **📋 COMPLETE ENDPOINT SUMMARY**

```java
// Authentication
POST   /api/auth/login
POST   /api/auth/register

// User Management (Admin only)
GET    /api/admin/users
GET    /api/admin/users/{id}
POST   /api/admin/users
PUT    /api/admin/users/{id}/role
PUT    /api/admin/users/{id}/activate
PUT    /api/admin/users/{id}/deactivate

// Reports & Analytics (Admin only) - For PDF Export
GET    /api/admin/reports/system-stats
GET    /api/admin/reports/user-growth
GET    /api/admin/reports/performance-metrics
GET    /api/admin/reports/appointment-analytics
GET    /api/admin/reports/revenue-analytics
```

### **� ENDPOINT SUMMARY**

```java
// Authentication
POST   /api/auth/login
POST   /api/auth/register

// User Management (Admin only)
GET    /api/admin/users
GET    /api/admin/users/{id}
POST   /api/admin/users
PUT    /api/admin/users/{id}/role
PUT    /api/admin/users/{id}/activate
PUT    /api/admin/users/{id}/deactivate
```

## �🔐 **1. AUTHENTICATION ENDPOINTS**

### **Login Endpoint**
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@RequestBody LoginRequest request) {
        // Validate credentials
        User user = userService.authenticate(request.getEmail(), request.getPassword());

        if (user == null) {
            throw new BadCredentialsException("Invalid email or password");
        }

        // Generate JWT token
        String token = jwtService.generateToken(user);

        // Return response
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        response.setUser(UserDTO.fromEntity(user));
        response.setMessage("Login successful");

        return ResponseEntity.ok(response);
    }
}

// Request/Response DTOs
public class LoginRequest {
    private String email;
    private String password;
    // getters and setters
}

public class LoginResponse {
    private String token;
    private UserDTO user;
    private String message;
    // getters and setters
}
```

### **Register Endpoint (Optional)**
```java
@PostMapping("/register")
public ResponseEntity<LoginResponse> register(@RequestBody RegisterRequest request) {
    // Validate request
    if (userService.existsByEmail(request.getEmail())) {
        throw new BadRequestException("Email already exists");
    }

    // Create user
    User user = new User();
    user.setName(request.getName());
    user.setEmail(request.getEmail());
    user.setPassword(passwordEncoder.encode(request.getPassword()));
    user.setPhoneNumber(request.getPhoneNumber());
    user.setAddress(request.getAddress());
    user.setDateOfBirth(request.getDateOfBirth());
    user.setGender(request.getGender());
    user.setRole(request.getRole() != null ? request.getRole() : Role.PATIENT);

    // Set status based on role
    if (user.getRole() == Role.DOCTOR || user.getRole() == Role.CLINIC) {
        user.setStatus(UserStatus.PENDING_APPROVAL);
    } else {
        user.setStatus(UserStatus.ACTIVE);
    }

    user = userService.save(user);

    // Generate token
    String token = jwtService.generateToken(user);

    LoginResponse response = new LoginResponse();
    response.setToken(token);
    response.setUser(UserDTO.fromEntity(user));
    response.setMessage("Registration successful");

    return ResponseEntity.ok(response);
}
```

## 👥 **2. USER MANAGEMENT ENDPOINTS**

### **Get All Users**
```java
@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminUserController {

    @GetMapping("/users")
    public ResponseEntity<List<UserDTO>> getAllUsers() {
        List<User> users = userService.findAll();
        List<UserDTO> userDTOs = users.stream()
            .map(UserDTO::fromEntity)
            .collect(Collectors.toList());

        return ResponseEntity.ok(userDTOs);
    }
}
```

### **Get Single User**
```java
@GetMapping("/users/{id}")
public ResponseEntity<UserDTO> getUser(@PathVariable Long id) {
    User user = userService.findById(id)
        .orElseThrow(() -> new UserNotFoundException("User not found with id: " + id));

    return ResponseEntity.ok(UserDTO.fromEntity(user));
}
```

### **Create User (Admin)**
```java
@PostMapping("/users")
public ResponseEntity<UserDTO> createUser(@RequestBody CreateUserRequest request) {
    // Validate email uniqueness
    if (userService.existsByEmail(request.getEmail())) {
        throw new BadRequestException("Email already exists. Please use a different email address.");
    }

    // Create user entity
    User user = new User();
    user.setName(request.getName());
    user.setEmail(request.getEmail());
    user.setPassword(passwordEncoder.encode(request.getPassword()));
    user.setPhoneNumber(request.getPhoneNumber());
    user.setAddress(request.getAddress());
    user.setDateOfBirth(request.getDateOfBirth());
    user.setGender(request.getGender());
    user.setRole(request.getRole());

    // Smart status management
    if (request.getRole() == Role.DOCTOR || request.getRole() == Role.CLINIC) {
        user.setStatus(UserStatus.PENDING_APPROVAL);
    } else {
        user.setStatus(UserStatus.ACTIVE);
    }

    user.setCreatedAt(LocalDateTime.now());
    user.setUpdatedAt(LocalDateTime.now());

    // Save user
    user = userService.save(user);

    return ResponseEntity.status(HttpStatus.CREATED)
        .body(UserDTO.fromEntity(user));
}

// Request DTO
public class CreateUserRequest {
    @NotBlank(message = "Name is required")
    private String name;

    @Email(message = "Please enter a valid email address")
    @NotBlank(message = "Email is required")
    private String email;

    @Size(min = 6, message = "Password must be at least 6 characters")
    @NotBlank(message = "Password is required")
    private String password;

    private String phoneNumber;
    private String address;
    private LocalDate dateOfBirth;

    @NotNull(message = "Gender is required")
    private Gender gender;

    @NotNull(message = "Role is required")
    private Role role;

    // getters and setters
}
```

### **Update User Role**
```java
@PutMapping("/users/{id}/role")
public ResponseEntity<ApiResponse> updateUserRole(
    @PathVariable Long id,
    @RequestBody UpdateRoleRequest request) {

    User user = userService.findById(id)
        .orElseThrow(() -> new UserNotFoundException("User not found"));

    // Check if same role
    if (user.getRole() == request.getNewRole()) {
        throw new BadRequestException("User already has the role: " + request.getNewRole());
    }

    Role oldRole = user.getRole();

    // Update role
    user.setRole(request.getNewRole());

    // Smart status management
    if (request.getNewRole() == Role.DOCTOR || request.getNewRole() == Role.CLINIC) {
        user.setStatus(UserStatus.PENDING_APPROVAL);
    } else {
        user.setStatus(UserStatus.ACTIVE);
    }

    user.setUpdatedAt(LocalDateTime.now());

    // Save user
    userService.save(user);

    // Log the change for audit
    auditService.logRoleChange(user.getId(), oldRole, request.getNewRole(),
        request.getReason(), getCurrentUser().getId());

    ApiResponse response = new ApiResponse();
    response.setSuccess(true);
    response.setMessage(String.format("User role changed from %s to %s successfully",
        oldRole, request.getNewRole()));

    return ResponseEntity.ok(response);
}

// Request DTO
public class UpdateRoleRequest {
    @NotNull(message = "New role is required")
    private Role newRole;

    private String reason;

    // getters and setters
}

// Alternative query parameter method
@PutMapping("/users/{id}/change-role")
public ResponseEntity<ApiResponse> updateUserRoleQuery(
    @PathVariable Long id,
    @RequestParam Role newRole) {

    UpdateRoleRequest request = new UpdateRoleRequest();
    request.setNewRole(newRole);
    request.setReason("Role change via query parameter");

    return updateUserRole(id, request);
}
```

### **Activate/Deactivate User**
```java
@PutMapping("/users/{id}/activate")
public ResponseEntity<ApiResponse> activateUser(@PathVariable Long id) {
    User user = userService.findById(id)
        .orElseThrow(() -> new UserNotFoundException("User not found"));

    user.setStatus(UserStatus.ACTIVE);
    user.setUpdatedAt(LocalDateTime.now());
    userService.save(user);

    ApiResponse response = new ApiResponse();
    response.setSuccess(true);
    response.setMessage("User activated successfully");

    return ResponseEntity.ok(response);
}

@PutMapping("/users/{id}/deactivate")
public ResponseEntity<ApiResponse> deactivateUser(@PathVariable Long id) {
    User user = userService.findById(id)
        .orElseThrow(() -> new UserNotFoundException("User not found"));

    user.setStatus(UserStatus.INACTIVE);
    user.setUpdatedAt(LocalDateTime.now());
    userService.save(user);

    ApiResponse response = new ApiResponse();
    response.setSuccess(true);
    response.setMessage("User deactivated successfully");

    return ResponseEntity.ok(response);
}
```

## 📊 **3. DATA MODELS**

### **User Entity**
```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    @Column(unique = true, nullable = false)
    private String email;

    @Column(nullable = false)
    private String password;

    private String phoneNumber;
    private String address;
    private LocalDate dateOfBirth;

    @Enumerated(EnumType.STRING)
    private Gender gender;

    @Enumerated(EnumType.STRING)
    private Role role;

    @Enumerated(EnumType.STRING)
    private UserStatus status;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastLogin;

    // getters and setters
}

// Enums
public enum Role {
    PATIENT, DOCTOR, CLINIC, CLINIC_STAFF, ADMIN
}

public enum UserStatus {
    ACTIVE, INACTIVE, PENDING_APPROVAL, SUSPENDED
}

public enum Gender {
    MALE, FEMALE, OTHER
}
```

### **UserDTO**
```java
public class UserDTO {
    private Long id;
    private String name;
    private String email;
    private String phoneNumber;
    private String address;
    private LocalDate dateOfBirth;
    private Gender gender;
    private Role role;
    private UserStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastLogin;

    public static UserDTO fromEntity(User user) {
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setName(user.getName());
        dto.setEmail(user.getEmail());
        dto.setPhoneNumber(user.getPhoneNumber());
        dto.setAddress(user.getAddress());
        dto.setDateOfBirth(user.getDateOfBirth());
        dto.setGender(user.getGender());
        dto.setRole(user.getRole());
        dto.setStatus(user.getStatus());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        dto.setLastLogin(user.getLastLogin());
        return dto;
    }

    // getters and setters
}
```

### **Response DTOs**
```java
public class ApiResponse {
    private boolean success;
    private String message;
    private Object data;

    // getters and setters
}
```

## 🔒 **4. SECURITY CONFIGURATION**

### **JWT Security**
```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

### **CORS Configuration**
```java
@Configuration
public class CorsConfig {

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("http://localhost:5173"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
```

## 🛡️ **5. ERROR HANDLING**

### **Global Exception Handler**
```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<ApiResponse> handleUserNotFound(UserNotFoundException ex) {
        ApiResponse response = new ApiResponse();
        response.setSuccess(false);
        response.setMessage(ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<ApiResponse> handleBadRequest(BadRequestException ex) {
        ApiResponse response = new ApiResponse();
        response.setSuccess(false);
        response.setMessage(ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse> handleValidation(MethodArgumentNotValidException ex) {
        String message = ex.getBindingResult().getFieldErrors().stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining(", "));

        ApiResponse response = new ApiResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
}
```

## 📋 **6. APPLICATION PROPERTIES**

```properties
# Database Configuration
spring.datasource.url=********************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# JWT Configuration
jwt.secret=your-secret-key
jwt.expiration=86400000

# Server Configuration
server.port=8083
```

## 🎯 **IMPLEMENTATION CHECKLIST**

### **Required Endpoints**
- [ ] POST /api/auth/login
- [ ] GET /api/admin/users
- [ ] POST /api/admin/users
- [ ] PUT /api/admin/users/{id}/role
- [ ] PUT /api/admin/users/{id}/activate
- [ ] PUT /api/admin/users/{id}/deactivate

### **Required Features**
- [ ] JWT authentication
- [ ] Role-based authorization
- [ ] Smart status management (DOCTOR/CLINIC → PENDING_APPROVAL)
- [ ] Email uniqueness validation
- [ ] Password encryption
- [ ] CORS configuration for localhost:5173
- [ ] Global error handling

### **Database Tables**
- [ ] users table with all required fields
- [ ] Proper indexes on email and role
- [ ] Audit table for role changes (optional)

**Once you implement these endpoints, your frontend will work seamlessly with permanent data storage!** 🚀
