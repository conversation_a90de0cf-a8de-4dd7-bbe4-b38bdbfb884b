import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authService } from '@/services/authService';
import { LoginRequest, AuthResponse, PatientRegisterRequest, DoctorRegisterRequest } from '@/types/api';
import { useAuth as useAuthContext } from '@/context/AuthContext';

export const useLogin = () => {
  const { login } = useAuthContext();
  
  return useMutation({
    mutationFn: async (credentials: LoginRequest): Promise<AuthResponse> => {
      return await login(credentials.email, credentials.password);
    },
    onSuccess: (data) => {
      // Handle successful login - context already handles token storage
      console.log('Login successful:', data);
    },
    onError: (error) => {
      console.error('Login failed:', error);
    }
  });
};

export const useRegister = () => {
  const { register } = useAuthContext();
  
  return useMutation({
    mutationFn: async (userData: PatientRegisterRequest | DoctorRegisterRequest): Promise<AuthResponse> => {
      return await register(userData);
    },
    onSuccess: (data) => {
      // Handle successful registration - context already handles token storage
      console.log('Registration successful:', data);
    },
    onError: (error) => {
      console.error('Registration failed:', error);
    }
  });
};

export const useLogout = () => {
  const { logout } = useAuthContext();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      try {
        await authService.logout();
      } catch (error) {
        // Even if the API call fails, we should still logout locally
        console.warn('Logout API call failed, but logging out locally:', error);
      }
    },
    onSuccess: () => {
      logout();
      queryClient.clear(); // Clear all cached data on logout
    }
  });
};

export const useValidateToken = () => {
  const { token, logout } = useAuthContext();
  
  return useQuery({
    queryKey: ['auth', 'validate'],
    queryFn: async () => {
      if (!token) throw new Error('No token available');
      return await authService.validateToken();
    },
    enabled: !!token,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Validate every 10 minutes
    onError: () => {
      // If token validation fails, logout the user
      logout();
    }
  });
};

export const useRefreshToken = () => {
  const { token } = useAuthContext();
  
  return useMutation({
    mutationFn: async () => {
      if (!token) throw new Error('No token available');
      return await authService.refreshToken();
    },
    onSuccess: (data) => {
      // Update token in localStorage
      localStorage.setItem('mediconnect_token', data.token);
    },
    onError: (error) => {
      console.error('Token refresh failed:', error);
      // Could trigger logout here if needed
    }
  });
};
