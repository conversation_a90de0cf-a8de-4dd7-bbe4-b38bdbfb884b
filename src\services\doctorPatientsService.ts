import { api } from './api';

export interface Patient {
  id: number;
  name: string;
  age: number;
  gender: 'Male' | 'Female' | 'Other';
  email: string;
  phone: string;
  address: string;
  dateOfBirth: string;
  lastVisit: string;
  nextAppointment?: string;
  condition: string;
  status: 'active' | 'inactive' | 'discharged';
  bloodType?: string;
  allergies?: string[];
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  medicalHistory?: MedicalRecord[];
  currentMedications?: Medication[];
}

export interface MedicalRecord {
  id: number;
  date: string;
  diagnosis: string;
  treatment: string;
  notes: string;
  doctorId: number;
  doctorName: string;
}

export interface Medication {
  id: number;
  name: string;
  dosage: string;
  frequency: string;
  startDate: string;
  endDate?: string;
  prescribedBy: string;
  status: 'active' | 'completed' | 'discontinued';
}

export interface PatientAppointment {
  id: number;
  patientId: number;
  doctorId: number;
  date: string;
  time: string;
  duration: number;
  type: 'consultation' | 'follow-up' | 'checkup' | 'emergency';
  status: 'scheduled' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
  notes?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
}

export interface PatientStats {
  totalPatients: number;
  activePatients: number;
  newPatientsThisMonth: number;
  upcomingAppointments: number;
  completedAppointmentsToday: number;
}

export const doctorPatientsService = {
  // Get all patients for a doctor
  async getPatients(doctorId: number, searchQuery?: string, status?: string): Promise<Patient[]> {
    try {
      console.log('🚀 Fetching patients for doctor:', doctorId);
      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (status) params.append('status', status);
      
      const response = await api.get(`/doctor/${doctorId}/patients?${params.toString()}`);
      console.log('✅ Patients fetched successfully:', response);
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error: any) {
      console.error('❌ Failed to fetch patients:', error);
      console.warn('⚠️ Using fallback patient data');
      return this.getFallbackPatients();
    }
  },

  // Get patient details by ID
  async getPatientById(patientId: number): Promise<Patient> {
    try {
      console.log('🚀 Fetching patient details for ID:', patientId);
      const response = await api.get(`/doctor/patients/${patientId}`);
      console.log('✅ Patient details fetched successfully:', response);
      return response.data || response;
    } catch (error: any) {
      console.error('❌ Failed to fetch patient details:', error);
      console.warn('⚠️ Using fallback patient data');
      return this.getFallbackPatients().find(p => p.id === patientId) || this.getFallbackPatients()[0];
    }
  },

  // Get patient's medical history
  async getPatientMedicalHistory(patientId: number): Promise<MedicalRecord[]> {
    try {
      console.log('🚀 Fetching medical history for patient:', patientId);
      const response = await api.get(`/doctor/patients/${patientId}/medical-history`);
      console.log('✅ Medical history fetched successfully:', response);
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error: any) {
      console.error('❌ Failed to fetch medical history:', error);
      return [];
    }
  },

  // Get patient's appointments
  async getPatientAppointments(patientId: number, doctorId: number): Promise<PatientAppointment[]> {
    try {
      console.log('🚀 Fetching appointments for patient:', patientId);
      const response = await api.get(`/doctor/${doctorId}/patients/${patientId}/appointments`);
      console.log('✅ Patient appointments fetched successfully:', response);
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error: any) {
      console.error('❌ Failed to fetch patient appointments:', error);
      return [];
    }
  },

  // Schedule new appointment
  async scheduleAppointment(appointment: Omit<PatientAppointment, 'id'>): Promise<{ success: boolean; message: string; appointmentId?: number }> {
    try {
      console.log('🚀 Scheduling new appointment:', appointment);
      const response = await api.post(`/doctor/${appointment.doctorId}/patients/${appointment.patientId}/appointments`, appointment);
      console.log('✅ Appointment scheduled successfully:', response);
      return {
        success: true,
        message: response.message || 'Appointment scheduled successfully',
        appointmentId: response.appointmentId || response.id
      };
    } catch (error: any) {
      console.error('❌ Failed to schedule appointment:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to schedule appointment'
      };
    }
  },

  // Update patient information
  async updatePatient(patientId: number, updates: Partial<Patient>): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🚀 Updating patient information:', patientId, updates);
      const response = await api.put(`/doctor/patients/${patientId}`, updates);
      console.log('✅ Patient updated successfully:', response);
      return {
        success: true,
        message: response.message || 'Patient information updated successfully'
      };
    } catch (error: any) {
      console.error('❌ Failed to update patient:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to update patient information'
      };
    }
  },

  // Add medical record
  async addMedicalRecord(patientId: number, record: Omit<MedicalRecord, 'id'>): Promise<{ success: boolean; message: string; recordId?: number }> {
    try {
      console.log('🚀 Adding medical record for patient:', patientId);
      const response = await api.post(`/doctor/patients/${patientId}/medical-history`, record);
      console.log('✅ Medical record added successfully:', response);
      return {
        success: true,
        message: response.message || 'Medical record added successfully',
        recordId: response.recordId || response.id
      };
    } catch (error: any) {
      console.error('❌ Failed to add medical record:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to add medical record'
      };
    }
  },

  // Get patient statistics for doctor
  async getPatientStats(doctorId: number): Promise<PatientStats> {
    try {
      console.log('🚀 Fetching patient statistics for doctor:', doctorId);
      const response = await api.get(`/doctor/${doctorId}/patients/stats`);
      console.log('✅ Patient statistics fetched successfully:', response);
      return response.data || response;
    } catch (error: any) {
      console.error('❌ Failed to fetch patient statistics:', error);
      console.warn('⚠️ Using fallback statistics');
      return {
        totalPatients: 45,
        activePatients: 42,
        newPatientsThisMonth: 8,
        upcomingAppointments: 12,
        completedAppointmentsToday: 6
      };
    }
  },

  // Search patients
  async searchPatients(doctorId: number, query: string): Promise<Patient[]> {
    try {
      console.log('🚀 Searching patients for doctor:', doctorId, 'query:', query);
      const response = await api.get(`/doctor/${doctorId}/patients/search?q=${encodeURIComponent(query)}`);
      console.log('✅ Patient search completed:', response);
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error: any) {
      console.error('❌ Failed to search patients:', error);
      return this.getFallbackPatients().filter(patient => 
        patient.name.toLowerCase().includes(query.toLowerCase()) ||
        patient.condition.toLowerCase().includes(query.toLowerCase())
      );
    }
  },

  // Fallback patient data
  getFallbackPatients(): Patient[] {
    return [
      {
        id: 1,
        name: "John Smith",
        age: 45,
        gender: "Male",
        email: "<EMAIL>",
        phone: "******-567-8901",
        address: "123 Main St, New York, NY 10001",
        dateOfBirth: "1979-03-15",
        lastVisit: "2024-01-15",
        nextAppointment: "2024-01-25",
        condition: "Hypertension",
        status: "active",
        bloodType: "O+",
        allergies: ["Penicillin", "Shellfish"],
        emergencyContact: {
          name: "Jane Smith",
          phone: "******-567-8902",
          relationship: "Spouse"
        }
      },
      {
        id: 2,
        name: "Mary Johnson",
        age: 32,
        gender: "Female",
        email: "<EMAIL>",
        phone: "******-567-8903",
        address: "456 Oak Ave, Brooklyn, NY 11201",
        dateOfBirth: "1992-07-22",
        lastVisit: "2024-01-10",
        nextAppointment: "2024-01-28",
        condition: "Diabetes Type 2",
        status: "active",
        bloodType: "A-",
        allergies: ["Latex"],
        emergencyContact: {
          name: "Robert Johnson",
          phone: "******-567-8904",
          relationship: "Father"
        }
      },
      {
        id: 3,
        name: "Robert Wilson",
        age: 58,
        gender: "Male",
        email: "<EMAIL>",
        phone: "******-567-8905",
        address: "789 Pine St, Queens, NY 11375",
        dateOfBirth: "1966-11-08",
        lastVisit: "2024-01-12",
        condition: "Arthritis",
        status: "active",
        bloodType: "B+",
        allergies: [],
        emergencyContact: {
          name: "Susan Wilson",
          phone: "******-567-8906",
          relationship: "Wife"
        }
      },
      {
        id: 4,
        name: "Emily Davis",
        age: 28,
        gender: "Female",
        email: "<EMAIL>",
        phone: "******-567-8907",
        address: "321 Elm St, Manhattan, NY 10002",
        dateOfBirth: "1996-04-18",
        lastVisit: "2024-01-08",
        nextAppointment: "2024-01-30",
        condition: "Asthma",
        status: "active",
        bloodType: "AB+",
        allergies: ["Dust mites", "Pollen"],
        emergencyContact: {
          name: "Michael Davis",
          phone: "******-567-8908",
          relationship: "Brother"
        }
      }
    ];
  }
};
