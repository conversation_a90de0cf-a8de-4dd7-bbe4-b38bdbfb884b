import { api } from './api';

export interface Patient {
  id: number;
  name: string;
  age: number;
  gender: 'Male' | 'Female' | 'Other';
  email: string;
  phone: string;
  address: string;
  dateOfBirth: string;
  lastVisit: string;
  nextAppointment?: string;
  condition: string;
  status: 'active' | 'inactive' | 'discharged';
  bloodType?: string;
  allergies?: string[];
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  medicalHistory?: MedicalRecord[];
  currentMedications?: Medication[];
}

export interface MedicalRecord {
  id: number;
  date: string;
  diagnosis: string;
  treatment: string;
  notes: string;
  doctorId: number;
  doctorName: string;
}

export interface Medication {
  id: number;
  name: string;
  dosage: string;
  frequency: string;
  startDate: string;
  endDate?: string;
  prescribedBy: string;
  status: 'active' | 'completed' | 'discontinued';
}

export interface PatientAppointment {
  id: number;
  patientId: number;
  doctorId: number;
  date: string;
  time: string;
  duration: number;
  type: 'consultation' | 'follow-up' | 'checkup' | 'emergency';
  status: 'scheduled' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
  notes?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
}

export interface PatientStats {
  totalPatients: number;
  activePatients: number;
  newPatientsThisMonth: number;
  upcomingAppointments: number;
  completedAppointmentsToday: number;
}

export const doctorPatientsService = {
  // Get all patients for a doctor
  async getPatients(doctorId: number, searchQuery?: string, status?: string): Promise<Patient[]> {
    console.log('🚀 Fetching patients for doctor:', doctorId);
    const params = new URLSearchParams();
    if (searchQuery) params.append('search', searchQuery);
    if (status) params.append('status', status);

    const response = await api.get(`/doctor/${doctorId}/patients?${params.toString()}`);
    console.log('✅ Patients fetched successfully:', response);

    // Handle backend response format
    const patients = response.success ? response.data : (Array.isArray(response) ? response : []);

    // Transform backend data to frontend format
    return patients.map((patient: any) => ({
      id: patient.id,
      name: patient.name,
      age: patient.age,
      gender: patient.gender,
      email: patient.email,
      phone: patient.phone,
      address: patient.address || '',
      dateOfBirth: patient.dateOfBirth || '',
      lastVisit: patient.lastVisit || '',
      nextAppointment: patient.nextAppointment || '',
      condition: patient.reason || 'General consultation',
      status: patient.status?.toLowerCase() || 'active',
      bloodType: patient.bloodType,
      allergies: patient.allergies ? patient.allergies.split(',').map((a: string) => a.trim()) : [],
      emergencyContact: patient.emergencyContact ? {
        name: patient.emergencyContact.split(' - ')[0] || '',
        phone: patient.emergencyContact,
        relationship: 'Emergency Contact'
      } : undefined,
      medicalHistory: [],
      currentMedications: []
    }));
  },

  // Get patient details by ID
  async getPatientById(patientId: number): Promise<Patient> {
    try {
      console.log('🚀 Fetching patient details for ID:', patientId);
      const response = await api.get(`/doctor/patients/${patientId}`);
      console.log('✅ Patient details fetched successfully:', response);
      return response.data || response;
    } catch (error: any) {
      console.error('❌ Failed to fetch patient details:', error);
      console.warn('⚠️ Using fallback patient data');
      return this.getFallbackPatients().find(p => p.id === patientId) || this.getFallbackPatients()[0];
    }
  },

  // Get patient's medical history
  async getPatientMedicalHistory(patientId: number): Promise<MedicalRecord[]> {
    try {
      console.log('🚀 Fetching medical history for patient:', patientId);
      const response = await api.get(`/doctor/patients/${patientId}/medical-history`);
      console.log('✅ Medical history fetched successfully:', response);
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error: any) {
      console.error('❌ Failed to fetch medical history:', error);
      return [];
    }
  },

  // Get patient's appointments
  async getPatientAppointments(patientId: number, doctorId: number): Promise<PatientAppointment[]> {
    try {
      console.log('🚀 Fetching appointments for patient:', patientId);
      const response = await api.get(`/doctor/${doctorId}/patients/${patientId}/appointments`);
      console.log('✅ Patient appointments fetched successfully:', response);
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error: any) {
      console.error('❌ Failed to fetch patient appointments:', error);
      return [];
    }
  },

  // Schedule new appointment
  async scheduleAppointment(appointment: Omit<PatientAppointment, 'id'>): Promise<{ success: boolean; message: string; appointmentId?: number }> {
    console.log('🚀 Scheduling new appointment:', appointment);

    // Transform frontend data to backend format
    const appointmentData = {
      date: appointment.date,
      time: appointment.time,
      duration: appointment.duration,
      type: appointment.type,
      status: appointment.status,
      notes: appointment.notes,
      clinicId: 1 // Default clinic ID - backend team should provide this from doctor's profile
    };

    const response = await api.post(`/doctor/${appointment.doctorId}/patients/${appointment.patientId}/appointments`, appointmentData);
    console.log('✅ Appointment scheduled successfully:', response);

    const result = response.success ? response.data : response;
    return {
      success: true,
      message: response.message || 'Appointment scheduled successfully',
      appointmentId: result.appointmentId || result.id
    };
  },

  // Update patient information
  async updatePatient(patientId: number, updates: Partial<Patient>): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🚀 Updating patient information:', patientId, updates);
      const response = await api.put(`/doctor/patients/${patientId}`, updates);
      console.log('✅ Patient updated successfully:', response);
      return {
        success: true,
        message: response.message || 'Patient information updated successfully'
      };
    } catch (error: any) {
      console.error('❌ Failed to update patient:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to update patient information'
      };
    }
  },

  // Add medical record
  async addMedicalRecord(patientId: number, record: Omit<MedicalRecord, 'id'>): Promise<{ success: boolean; message: string; recordId?: number }> {
    try {
      console.log('🚀 Adding medical record for patient:', patientId);
      const response = await api.post(`/doctor/patients/${patientId}/medical-history`, record);
      console.log('✅ Medical record added successfully:', response);
      return {
        success: true,
        message: response.message || 'Medical record added successfully',
        recordId: response.recordId || response.id
      };
    } catch (error: any) {
      console.error('❌ Failed to add medical record:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to add medical record'
      };
    }
  },

  // Get patient statistics for doctor
  async getPatientStats(doctorId: number): Promise<PatientStats> {
    console.log('🚀 Fetching patient statistics for doctor:', doctorId);
    const response = await api.get(`/doctor/${doctorId}/patients/statistics`);
    console.log('✅ Patient statistics fetched successfully:', response);

    const stats = response.success ? response.data : response;

    // Transform backend data to frontend format
    return {
      totalPatients: stats.totalPatients || 0,
      activePatients: stats.activePatients || 0,
      newPatientsThisMonth: stats.recentPatients || 0,
      upcomingAppointments: stats.upcomingAppointments || 0,
      completedAppointmentsToday: 0 // This would need to be calculated from today's completed appointments
    };
  },

  // Search patients
  async searchPatients(doctorId: number, query: string): Promise<Patient[]> {
    console.log('🚀 Searching patients for doctor:', doctorId, 'query:', query);
    const response = await api.get(`/doctor/${doctorId}/patients/search?q=${encodeURIComponent(query)}`);
    console.log('✅ Patient search completed:', response);

    const patients = response.success ? response.data : (Array.isArray(response) ? response : []);

    // Transform backend data to frontend format (same as getPatients)
    return patients.map((patient: any) => ({
      id: patient.id,
      name: patient.name,
      age: patient.age,
      gender: patient.gender,
      email: patient.email,
      phone: patient.phone,
      address: patient.address || '',
      dateOfBirth: patient.dateOfBirth || '',
      lastVisit: patient.lastVisit || '',
      nextAppointment: patient.nextAppointment || '',
      condition: patient.reason || 'General consultation',
      status: patient.status?.toLowerCase() || 'active',
      bloodType: patient.bloodType,
      allergies: patient.allergies ? patient.allergies.split(',').map((a: string) => a.trim()) : [],
      emergencyContact: patient.emergencyContact ? {
        name: patient.emergencyContact.split(' - ')[0] || '',
        phone: patient.emergencyContact,
        relationship: 'Emergency Contact'
      } : undefined,
      medicalHistory: [],
      currentMedications: []
    }));
  }
};
