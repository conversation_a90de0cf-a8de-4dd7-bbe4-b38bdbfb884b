import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Download,
  FileText,
  BarChart3,
  Users,
  Calendar,
  TrendingUp,
  Loader2,
  CheckCircle
} from "lucide-react";

interface ExportReportsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (options: ExportOptions) => Promise<void>;
  isExporting?: boolean;
}

export interface ExportOptions {
  reportType: 'system' | 'users' | 'appointments' | 'performance' | 'revenue' | 'custom';
  format: 'pdf' | 'excel' | 'csv';
  dateRange: 'last7days' | 'last30days' | 'last3months' | 'last6months' | 'lastyear' | 'custom';
  includeCharts: boolean;
  includeSummary: boolean;
  includeDetails: boolean;
  sections: string[];
}

const ExportReportsModal = ({ isOpen, onClose, onExport, isExporting = false }: ExportReportsModalProps) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    reportType: 'system',
    format: 'pdf',
    dateRange: 'last30days',
    includeCharts: true,
    includeSummary: true,
    includeDetails: true,
    sections: ['overview', 'users', 'appointments', 'performance']
  });

  const reportTypes = [
    {
      value: 'system',
      label: 'System Overview Report',
      description: 'Comprehensive system analytics and metrics',
      icon: BarChart3,
      color: 'bg-blue-100 text-blue-800'
    },
    {
      value: 'users',
      label: 'User Management Report',
      description: 'User statistics, roles, and activity',
      icon: Users,
      color: 'bg-green-100 text-green-800'
    },
    {
      value: 'appointments',
      label: 'Appointment Analytics',
      description: 'Appointment trends and statistics',
      icon: Calendar,
      color: 'bg-purple-100 text-purple-800'
    },
    {
      value: 'performance',
      label: 'Performance Metrics',
      description: 'System performance and uptime data',
      icon: TrendingUp,
      color: 'bg-orange-100 text-orange-800'
    }
  ];

  const dateRanges = [
    { value: 'last7days', label: 'Last 7 Days' },
    { value: 'last30days', label: 'Last 30 Days' },
    { value: 'last3months', label: 'Last 3 Months' },
    { value: 'last6months', label: 'Last 6 Months' },
    { value: 'lastyear', label: 'Last Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const availableSections = [
    { id: 'overview', label: 'Executive Summary', description: 'High-level metrics and KPIs' },
    { id: 'users', label: 'User Analytics', description: 'User growth and demographics' },
    { id: 'appointments', label: 'Appointment Data', description: 'Booking trends and statistics' },
    { id: 'performance', label: 'System Performance', description: 'Technical metrics and uptime' },
    { id: 'revenue', label: 'Revenue Analytics', description: 'Financial performance data' },
    { id: 'security', label: 'Security Metrics', description: 'Login attempts and security events' }
  ];

  const handleSectionToggle = (sectionId: string, checked: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      sections: checked
        ? [...prev.sections, sectionId]
        : prev.sections.filter(s => s !== sectionId)
    }));
  };

  const handleExport = async () => {
    try {
      await onExport(exportOptions);
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const selectedReportType = reportTypes.find(type => type.value === exportOptions.reportType);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5 text-blue-600" />
            Export Reports
          </DialogTitle>
          <DialogDescription>
            Generate and download professional PDF reports with your system data.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Report Type Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Report Type</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {reportTypes.map((type) => (
                <Card
                  key={type.value}
                  className={`cursor-pointer transition-all ${
                    exportOptions.reportType === type.value
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setExportOptions(prev => ({ ...prev, reportType: type.value as ExportOptions['reportType'] }))}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${type.color}`}>
                        <type.icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-sm">{type.label}</CardTitle>
                        <CardDescription className="text-xs">{type.description}</CardDescription>
                      </div>
                      {exportOptions.reportType === type.value && (
                        <CheckCircle className="h-5 w-5 text-blue-600" />
                      )}
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>

          {/* Export Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Format Selection */}
            <div className="space-y-3">
              <Label htmlFor="format">Export Format</Label>
              <Select
                value={exportOptions.format}
                onValueChange={(value: ExportOptions['format']) => setExportOptions(prev => ({ ...prev, format: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      PDF Document
                    </div>
                  </SelectItem>
                  <SelectItem value="excel" disabled>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Excel Spreadsheet (Coming Soon)
                    </div>
                  </SelectItem>
                  <SelectItem value="csv" disabled>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      CSV File (Coming Soon)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date Range */}
            <div className="space-y-3">
              <Label htmlFor="dateRange">Date Range</Label>
              <Select
                value={exportOptions.dateRange}
                onValueChange={(value: ExportOptions['dateRange']) => setExportOptions(prev => ({ ...prev, dateRange: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  {dateRanges.map((range) => (
                    <SelectItem key={range.value} value={range.value}>
                      {range.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Include Options */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Include in Report</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeSummary"
                  checked={exportOptions.includeSummary}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeSummary: checked as boolean }))
                  }
                />
                <Label htmlFor="includeSummary" className="text-sm">Executive Summary</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeCharts"
                  checked={exportOptions.includeCharts}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeCharts: checked as boolean }))
                  }
                />
                <Label htmlFor="includeCharts" className="text-sm">Charts & Graphs</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeDetails"
                  checked={exportOptions.includeDetails}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeDetails: checked as boolean }))
                  }
                />
                <Label htmlFor="includeDetails" className="text-sm">Detailed Data</Label>
              </div>
            </div>
          </div>

          {/* Section Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Report Sections</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {availableSections.map((section) => (
                <div key={section.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                  <Checkbox
                    id={section.id}
                    checked={exportOptions.sections.includes(section.id)}
                    onCheckedChange={(checked) => handleSectionToggle(section.id, checked as boolean)}
                  />
                  <div className="flex-1">
                    <Label htmlFor={section.id} className="text-sm font-medium cursor-pointer">
                      {section.label}
                    </Label>
                    <p className="text-xs text-gray-600 mt-1">{section.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Preview */}
          {selectedReportType && (
            <Alert>
              <FileText className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-medium">Report Preview:</p>
                  <div className="flex items-center gap-2">
                    <Badge className={selectedReportType.color}>
                      {selectedReportType.label}
                    </Badge>
                    <span className="text-sm">•</span>
                    <span className="text-sm">{exportOptions.format.toUpperCase()} format</span>
                    <span className="text-sm">•</span>
                    <span className="text-sm">{exportOptions.sections.length} sections</span>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isExporting}>
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting || exportOptions.sections.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating Report...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export Report
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ExportReportsModal;
