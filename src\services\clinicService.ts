import { api } from './api';
import { ClinicProfile, AppointmentResponse, AnnouncementRequest, DoctorProfile } from '@/types/api';

export const clinicService = {
  // Clinic Profile
  async getProfile(clinicId: number): Promise<ClinicProfile> {
    return await api.get('/clinics/profile', { clinicId });
  },

  async updateProfile(clinicId: number, profileData: Partial<ClinicProfile>): Promise<ClinicProfile> {
    return await api.put('/clinics/profile', profileData, { clinicId });
  },

  // Appointment Management
  async getAppointments(clinicId: number, status?: string, date?: string): Promise<AppointmentResponse[]> {
    const params: any = { clinicId };
    if (status) params.status = status;
    if (date) params.date = date;
    return await api.get('/clinics/appointments', params);
  },

  async getTodayAppointments(clinicId: number): Promise<AppointmentResponse[]> {
    return await api.get('/clinics/appointments/today', { clinicId });
  },

  async getUpcomingAppointments(clinicId: number): Promise<AppointmentResponse[]> {
    return await api.get('/clinics/appointments/upcoming', { clinicId });
  },

  async updateAppointmentStatus(appointmentId: number, status: string): Promise<void> {
    return await api.put(`/appointments/${appointmentId}/status`, null, { status });
  },

  // Staff Management
  async getStaff(clinicId: number): Promise<any[]> {
    return await api.get('/clinics/staff', { clinicId });
  },

  async getDoctors(clinicId: number): Promise<DoctorProfile[]> {
    return await api.get('/clinics/staff/doctors', { clinicId });
  },

  async addDoctor(clinicId: number, doctorData: any): Promise<void> {
    return await api.post('/clinics/staff/doctors', doctorData, { clinicId });
  },

  async removeDoctor(clinicId: number, doctorId: number): Promise<void> {
    return await api.delete(`/clinics/staff/doctors/${doctorId}`, { clinicId });
  },

  async addStaffMember(clinicId: number, staffData: any): Promise<void> {
    return await api.post('/clinics/staff', staffData, { clinicId });
  },

  async updateStaffMember(staffId: number, staffData: any): Promise<void> {
    return await api.put(`/clinics/staff/${staffId}`, staffData);
  },

  async removeStaffMember(staffId: number): Promise<void> {
    return await api.delete(`/clinics/staff/${staffId}`);
  },

  // Announcements
  async getAnnouncements(clinicId: number): Promise<any[]> {
    return await api.get('/clinics/announcements', { clinicId });
  },

  async createAnnouncement(clinicId: number, announcementData: AnnouncementRequest): Promise<void> {
    return await api.post('/clinics/announcements', announcementData, { clinicId });
  },

  async updateAnnouncement(clinicId: number, announcementId: number, announcementData: Partial<AnnouncementRequest>): Promise<void> {
    return await api.put(`/clinics/announcements/${announcementId}`, announcementData, { clinicId });
  },

  async deleteAnnouncement(clinicId: number, announcementId: number): Promise<void> {
    return await api.delete(`/clinics/announcements/${announcementId}`, { clinicId });
  },

  // Schedule Management
  async getSchedule(clinicId: number, date?: string): Promise<any> {
    const params: any = { clinicId };
    if (date) params.date = date;
    return await api.get('/clinics/schedule', params);
  },

  async updateSchedule(clinicId: number, scheduleData: any): Promise<void> {
    return await api.put('/clinics/schedule', scheduleData, { clinicId });
  },

  // Statistics and Reports
  async getDashboardStats(clinicId: number): Promise<any> {
    return await api.get('/clinics/dashboard/stats', { clinicId });
  },

  async getAppointmentStats(clinicId: number, period: string = '30d'): Promise<any> {
    return await api.get('/clinics/appointments/stats', { clinicId, period });
  },

  async getRevenueStats(clinicId: number, period: string = '30d'): Promise<any> {
    return await api.get('/clinics/revenue/stats', { clinicId, period });
  },

  async getPatientStats(clinicId: number): Promise<any> {
    return await api.get('/clinics/patients/stats', { clinicId });
  },

  // Facilities Management
  async getFacilities(clinicId: number): Promise<any[]> {
    return await api.get('/clinics/facilities', { clinicId });
  },

  async addFacility(clinicId: number, facilityData: any): Promise<void> {
    return await api.post('/clinics/facilities', facilityData, { clinicId });
  },

  async updateFacility(facilityId: number, facilityData: any): Promise<void> {
    return await api.put(`/clinics/facilities/${facilityId}`, facilityData);
  },

  async deleteFacility(facilityId: number): Promise<void> {
    return await api.delete(`/clinics/facilities/${facilityId}`);
  },

  // Equipment Management
  async getEquipment(clinicId: number): Promise<any[]> {
    return await api.get('/clinics/equipment', { clinicId });
  },

  async addEquipment(clinicId: number, equipmentData: any): Promise<void> {
    return await api.post('/clinics/equipment', equipmentData, { clinicId });
  },

  async updateEquipment(equipmentId: number, equipmentData: any): Promise<void> {
    return await api.put(`/clinics/equipment/${equipmentId}`, equipmentData);
  },

  async deleteEquipment(equipmentId: number): Promise<void> {
    return await api.delete(`/clinics/equipment/${equipmentId}`);
  },

  // Billing and Payments
  async getBillingInfo(clinicId: number): Promise<any> {
    return await api.get('/clinics/billing', { clinicId });
  },

  async updateBillingInfo(clinicId: number, billingData: any): Promise<void> {
    return await api.put('/clinics/billing', billingData, { clinicId });
  },

  async getPaymentHistory(clinicId: number, page: number = 1, limit: number = 50): Promise<any> {
    return await api.get('/clinics/payments/history', { clinicId, page, limit });
  },

  // Notifications
  async getNotifications(clinicId: number): Promise<any[]> {
    return await api.get('/clinics/notifications', { clinicId });
  },

  async markNotificationAsRead(notificationId: number): Promise<void> {
    return await api.put(`/clinics/notifications/${notificationId}/read`);
  }
};
