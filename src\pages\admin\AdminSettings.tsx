
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Shield, ArrowLeft, Save, AlertCircle, CheckCircle, Download, Upload, RotateCcw, TestTube } from "lucide-react";
import Header from "@/components/common/Header";
import { useSettingsData, useSaveSecuritySettings, useResetSettings, useTestSettings, useExportSettings, useImportSettings } from "@/hooks/useSettings";
import { SecuritySettings } from "@/services/settingsService";

const AdminSettings = () => {
  const navigate = useNavigate();

  // Fetch settings data from backend
  const {
    settings: backendSettings,
    metrics,
    auditTrail,
    isLoading,
    isError,
    error
  } = useSettingsData();

  // Mutations for settings operations
  const saveSettingsMutation = useSaveSecuritySettings();
  const resetSettingsMutation = useResetSettings();
  const testSettingsMutation = useTestSettings();
  const exportSettingsMutation = useExportSettings();
  const importSettingsMutation = useImportSettings();

  // Local state for form
  const [localSettings, setLocalSettings] = useState<SecuritySettings>({
    twoFactorAuthEnabled: true,
    strongPasswordPolicyEnabled: true,
    sessionTimeoutMinutes: 30,
    maxLoginAttempts: 5,
    dataEncryptionEnabled: true,
    auditLoggingEnabled: true
  });

  const [hasChanges, setHasChanges] = useState(false);

  // Update local settings when backend data loads
  useEffect(() => {
    if (backendSettings) {
      setLocalSettings(backendSettings);
      setHasChanges(false);
    }
  }, [backendSettings]);

  // Handle form changes
  const handleSettingChange = (key: keyof SecuritySettings, value: boolean | number) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  // Save settings to backend
  const handleSave = () => {
    saveSettingsMutation.mutate(localSettings, {
      onSuccess: () => {
        setHasChanges(false);
      }
    });
  };

  // Reset to defaults
  const handleReset = () => {
    resetSettingsMutation.mutate(undefined, {
      onSuccess: () => {
        setHasChanges(false);
      }
    });
  };

  // Test current settings
  const handleTest = () => {
    testSettingsMutation.mutate(localSettings);
  };

  // Export settings
  const handleExport = () => {
    exportSettingsMutation.mutate();
  };

  // Import settings
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      importSettingsMutation.mutate(file, {
        onSuccess: () => {
          setHasChanges(false);
        }
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="pt-20 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                onClick={() => navigate('/admin')}
                className="flex items-center"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Security Settings</h1>
                <p className="text-gray-600">Manage system security and access controls</p>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Authentication Settings
                </CardTitle>
                <CardDescription>Configure user authentication and access policies</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Two-Factor Authentication</Label>
                    <p className="text-sm text-gray-600">Require 2FA for all admin accounts</p>
                  </div>
                  <Switch
                    checked={localSettings.twoFactorAuthEnabled}
                    onCheckedChange={(checked) => handleSettingChange('twoFactorAuthEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Strong Password Policy</Label>
                    <p className="text-sm text-gray-600">Enforce complex password requirements</p>
                  </div>
                  <Switch
                    checked={localSettings.strongPasswordPolicyEnabled}
                    onCheckedChange={(checked) => handleSettingChange('strongPasswordPolicyEnabled', checked)}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      min="5"
                      max="480"
                      value={localSettings.sessionTimeoutMinutes}
                      onChange={(e) => handleSettingChange('sessionTimeoutMinutes', parseInt(e.target.value) || 30)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="loginAttempts">Max Login Attempts</Label>
                    <Input
                      id="loginAttempts"
                      type="number"
                      min="1"
                      max="10"
                      value={localSettings.maxLoginAttempts}
                      onChange={(e) => handleSettingChange('maxLoginAttempts', parseInt(e.target.value) || 5)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Data Security</CardTitle>
                <CardDescription>Configure data protection and monitoring</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Data Encryption</Label>
                    <p className="text-sm text-gray-600">Encrypt sensitive data at rest</p>
                  </div>
                  <Switch
                    checked={localSettings.dataEncryptionEnabled}
                    onCheckedChange={(checked) => handleSettingChange('dataEncryptionEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Audit Logging</Label>
                    <p className="text-sm text-gray-600">Log all admin and system actions</p>
                  </div>
                  <Switch
                    checked={localSettings.auditLoggingEnabled}
                    onCheckedChange={(checked) => handleSettingChange('auditLoggingEnabled', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Loading State */}
            {isLoading && (
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-1/4" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-4 w-1/3" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Error State */}
            {isError && (
              <Alert className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  <div className="font-medium">Failed to load settings</div>
                  <div className="text-sm mt-1">
                    {error?.message || 'Unable to fetch settings from the backend. Using default values.'}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Security Metrics */}
            {metrics && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                    Security Overview
                  </CardTitle>
                  <CardDescription>Current security configuration status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{metrics.securityScore || 85}%</div>
                      <div className="text-sm text-green-600">Security Score</div>
                    </div>
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{metrics.enabledFeatures || 5}/{metrics.totalFeatures || 6}</div>
                      <div className="text-sm text-blue-600">Features Enabled</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{auditTrail.length}</div>
                      <div className="text-sm text-purple-600">Recent Changes</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">
                        {hasChanges ? 'Pending' : 'Saved'}
                      </div>
                      <div className="text-sm text-orange-600">Status</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3 justify-between">
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={handleTest}
                  disabled={testSettingsMutation.isPending}
                >
                  <TestTube className="h-4 w-4 mr-2" />
                  {testSettingsMutation.isPending ? 'Testing...' : 'Test Settings'}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleExport}
                  disabled={exportSettingsMutation.isPending}
                >
                  <Download className="h-4 w-4 mr-2" />
                  {exportSettingsMutation.isPending ? 'Exporting...' : 'Export'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('import-file')?.click()}
                  disabled={importSettingsMutation.isPending}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  {importSettingsMutation.isPending ? 'Importing...' : 'Import'}
                </Button>
                <input
                  id="import-file"
                  type="file"
                  accept=".json"
                  onChange={handleImport}
                  className="hidden"
                />
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={handleReset}
                  disabled={resetSettingsMutation.isPending || !hasChanges}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  {resetSettingsMutation.isPending ? 'Resetting...' : 'Reset to Defaults'}
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={saveSettingsMutation.isPending || !hasChanges}
                  className="flex items-center"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {saveSettingsMutation.isPending ? 'Saving...' : 'Save Settings'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings;
