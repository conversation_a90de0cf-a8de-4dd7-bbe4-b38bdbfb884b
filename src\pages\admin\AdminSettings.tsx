
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Shield, ArrowLeft, Save } from "lucide-react";
import Header from "@/components/common/Header";
import { useSecuritySettings, useSaveSecuritySettings } from "@/hooks/useSettings";
import { SecuritySettings } from "@/services/settingsService";

const AdminSettings = () => {
  const navigate = useNavigate();

  // Fetch settings data from backend
  const { data: backendSettings, isLoading, isError } = useSecuritySettings();

  // Save settings mutation
  const saveSettingsMutation = useSaveSecuritySettings();

  // Local state for form
  const [localSettings, setLocalSettings] = useState<SecuritySettings>({
    twoFactorAuthEnabled: true,
    strongPasswordPolicyEnabled: true,
    sessionTimeoutMinutes: 30,
    maxLoginAttempts: 5,
    dataEncryptionEnabled: true,
    auditLoggingEnabled: true
  });

  // Update local settings when backend data loads
  useEffect(() => {
    if (backendSettings) {
      setLocalSettings(backendSettings);
    }
  }, [backendSettings]);

  // Handle form changes
  const handleSettingChange = (key: keyof SecuritySettings, value: boolean | number) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Save settings to backend
  const handleSave = () => {
    saveSettingsMutation.mutate(localSettings);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="pt-20 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                onClick={() => navigate('/admin')}
                className="flex items-center"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Security Settings</h1>
                <p className="text-gray-600">Manage system security and access controls</p>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Authentication Settings
                </CardTitle>
                <CardDescription>Configure user authentication and access policies</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Two-Factor Authentication</Label>
                    <p className="text-sm text-gray-600">Require 2FA for all admin accounts</p>
                  </div>
                  <Switch
                    checked={localSettings.twoFactorAuthEnabled}
                    onCheckedChange={(checked) => handleSettingChange('twoFactorAuthEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Strong Password Policy</Label>
                    <p className="text-sm text-gray-600">Enforce complex password requirements</p>
                  </div>
                  <Switch
                    checked={localSettings.strongPasswordPolicyEnabled}
                    onCheckedChange={(checked) => handleSettingChange('strongPasswordPolicyEnabled', checked)}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      min="5"
                      max="480"
                      value={localSettings.sessionTimeoutMinutes}
                      onChange={(e) => handleSettingChange('sessionTimeoutMinutes', parseInt(e.target.value) || 30)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="loginAttempts">Max Login Attempts</Label>
                    <Input
                      id="loginAttempts"
                      type="number"
                      min="1"
                      max="10"
                      value={localSettings.maxLoginAttempts}
                      onChange={(e) => handleSettingChange('maxLoginAttempts', parseInt(e.target.value) || 5)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Data Security</CardTitle>
                <CardDescription>Configure data protection and monitoring</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Data Encryption</Label>
                    <p className="text-sm text-gray-600">Encrypt sensitive data at rest</p>
                  </div>
                  <Switch
                    checked={localSettings.dataEncryptionEnabled}
                    onCheckedChange={(checked) => handleSettingChange('dataEncryptionEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Audit Logging</Label>
                    <p className="text-sm text-gray-600">Log all admin and system actions</p>
                  </div>
                  <Switch
                    checked={localSettings.auditLoggingEnabled}
                    onCheckedChange={(checked) => handleSettingChange('auditLoggingEnabled', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button
                onClick={handleSave}
                disabled={saveSettingsMutation.isPending}
                className="flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {saveSettingsMutation.isPending ? 'Saving...' : 'Save Settings'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings;
