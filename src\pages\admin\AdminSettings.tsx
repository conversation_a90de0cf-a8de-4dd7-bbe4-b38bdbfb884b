
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Shield, ArrowLeft, Save, AlertTriangle, Loader2 } from "lucide-react";
import Header from "@/components/common/Header";
import { useSystemSettings, useUpdateSystemSettings } from "@/hooks/useAdmin";
import { useToast } from "@/hooks/use-toast";

const AdminSettings = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch current settings from backend
  const {
    data: backendSettings,
    isLoading,
    isError,
    error
  } = useSystemSettings();

  // Mutation for updating settings
  const updateSettingsMutation = useUpdateSystemSettings();

  // Local state for form
  const [settings, setSettings] = useState({
    twoFactorAuth: true,
    passwordPolicy: true,
    sessionTimeout: "30",
    loginAttempts: "5",
    dataEncryption: true,
    auditLogging: true
  });

  // Update local state when backend data is loaded
  useEffect(() => {
    if (backendSettings) {
      setSettings({
        twoFactorAuth: backendSettings.twoFactorAuthEnabled,
        passwordPolicy: backendSettings.strongPasswordPolicyEnabled,
        sessionTimeout: backendSettings.sessionTimeoutMinutes.toString(),
        loginAttempts: backendSettings.maxLoginAttempts.toString(),
        dataEncryption: backendSettings.dataEncryptionEnabled,
        auditLogging: backendSettings.auditLoggingEnabled
      });
    }
  }, [backendSettings]);

  const handleSave = () => {
    // Transform local state to backend format
    const backendFormat = {
      twoFactorAuthEnabled: settings.twoFactorAuth,
      strongPasswordPolicyEnabled: settings.passwordPolicy,
      sessionTimeoutMinutes: parseInt(settings.sessionTimeout),
      maxLoginAttempts: parseInt(settings.loginAttempts),
      dataEncryptionEnabled: settings.dataEncryption,
      auditLoggingEnabled: settings.auditLogging
    };

    updateSettingsMutation.mutate(backendFormat, {
      onSuccess: () => {
        toast({
          title: "Settings Saved",
          description: "Security settings have been updated successfully.",
        });
      },
      onError: (error: Error) => {
        toast({
          title: "Save Failed",
          description: error.message || "Failed to save settings. Please try again.",
          variant: "destructive",
        });
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="pt-20 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                onClick={() => navigate('/admin')}
                className="flex items-center"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Security Settings</h1>
                <p className="text-gray-600">Manage system security and access controls</p>
              </div>
            </div>
          </div>

          {/* Error Alert */}
          {isError && (
            <Alert className="mb-6 border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <div className="font-medium">Failed to load settings</div>
                <div className="text-sm mt-1">
                  {error?.message || 'Unable to fetch settings from the backend. Using default values.'}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Authentication Settings
                </CardTitle>
                <CardDescription>Configure user authentication and access policies</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {isLoading ? (
                  // Loading skeletons
                  <div className="space-y-6">
                    {Array.from({ length: 4 }).map((_, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-48" />
                        </div>
                        <Skeleton className="h-6 w-12" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-base">Two-Factor Authentication</Label>
                        <p className="text-sm text-gray-600">Require 2FA for all admin accounts</p>
                      </div>
                      <Switch
                        checked={settings.twoFactorAuth}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, twoFactorAuth: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-base">Strong Password Policy</Label>
                        <p className="text-sm text-gray-600">Enforce complex password requirements</p>
                      </div>
                      <Switch
                        checked={settings.passwordPolicy}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, passwordPolicy: checked }))}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                        <Input
                          id="sessionTimeout"
                          type="number"
                          value={settings.sessionTimeout}
                          onChange={(e) => setSettings(prev => ({ ...prev, sessionTimeout: e.target.value }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="loginAttempts">Max Login Attempts</Label>
                        <Input
                          id="loginAttempts"
                          type="number"
                          value={settings.loginAttempts}
                          onChange={(e) => setSettings(prev => ({ ...prev, loginAttempts: e.target.value }))}
                        />
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Data Security</CardTitle>
                <CardDescription>Configure data protection and monitoring</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {isLoading ? (
                  // Loading skeletons
                  <div className="space-y-6">
                    {Array.from({ length: 2 }).map((_, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-48" />
                        </div>
                        <Skeleton className="h-6 w-12" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-base">Data Encryption</Label>
                        <p className="text-sm text-gray-600">Encrypt sensitive data at rest</p>
                      </div>
                      <Switch
                        checked={settings.dataEncryption}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, dataEncryption: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-base">Audit Logging</Label>
                        <p className="text-sm text-gray-600">Log all admin and system actions</p>
                      </div>
                      <Switch
                        checked={settings.auditLogging}
                        onCheckedChange={(checked) => setSettings(prev => ({ ...prev, auditLogging: checked }))}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button
                onClick={handleSave}
                disabled={updateSettingsMutation.isPending || isLoading}
                className="flex items-center"
              >
                {updateSettingsMutation.isPending ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {updateSettingsMutation.isPending ? 'Saving...' : 'Save Settings'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings;
