
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Check, X, Eye, FileText, Users, Clock, AlertCircle, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { usePendingDoctors, useApproveDoctor, useRejectDoctor, useAllUsers } from "@/hooks/useAdmin";
import { User } from "@/types/api";
import DoctorDetailsModal from "@/components/admin/DoctorDetailsModal";
import Header from "@/components/common/Header";

const DoctorApproval = () => {
  const { toast } = useToast();
  const [selectedDoctor, setSelectedDoctor] = useState<User | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch data using React Query hooks
  const {
    data: pendingDoctors = [],
    isLoading: pendingLoading,
    error: pendingError,
    refetch: refetchPending
  } = usePendingDoctors();

  const {
    data: allUsers = [],
    isLoading: usersLoading
  } = useAllUsers();

  // Mutations for approve/reject
  const approveMutation = useApproveDoctor();
  const rejectMutation = useRejectDoctor();

  // Filter approved and rejected doctors from all users
  const approvedDoctors = allUsers.filter(user =>
    user.role === 'DOCTOR' && (user.status === 'ACTIVE' || user.status === 'APPROVED')
  );

  const rejectedDoctors = allUsers.filter(user =>
    user.role === 'DOCTOR' && (user.status === 'SUSPENDED' || user.status === 'REJECTED' || user.status === 'INACTIVE')
  );

  // Handler functions
  const handleViewDetails = (doctor: User) => {
    setSelectedDoctor(doctor);
    setIsModalOpen(true);
  };

  const handleApprove = async (id: number) => {
    try {
      await approveMutation.mutateAsync(id);
      toast({
        title: "Doctor Approved",
        description: "The doctor application has been approved successfully.",
      });
      setIsModalOpen(false);
    } catch (error: any) {
      toast({
        title: "Approval Failed",
        description: error.message || "Failed to approve doctor. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleReject = async (id: number) => {
    try {
      await rejectMutation.mutateAsync(id);
      toast({
        title: "Doctor Rejected",
        description: "The doctor application has been rejected.",
      });
      setIsModalOpen(false);
    } catch (error: any) {
      toast({
        title: "Rejection Failed",
        description: error.message || "Failed to reject doctor. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRetry = () => {
    refetchPending();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="pt-20 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Doctor Approval</h1>
            <p className="text-gray-600">Review and approve doctor registration applications</p>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pending Approval</p>
                      {pendingLoading ? (
                        <Skeleton className="h-8 w-16" />
                      ) : (
                        <p className="text-2xl font-bold text-yellow-600">{pendingDoctors.length}</p>
                      )}
                    </div>
                    <Clock className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Approved</p>
                      {usersLoading ? (
                        <Skeleton className="h-8 w-16" />
                      ) : (
                        <p className="text-2xl font-bold text-green-600">{approvedDoctors.length}</p>
                      )}
                    </div>
                    <Check className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Rejected</p>
                      {usersLoading ? (
                        <Skeleton className="h-8 w-16" />
                      ) : (
                        <p className="text-2xl font-bold text-red-600">{rejectedDoctors.length}</p>
                      )}
                    </div>
                    <X className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <Tabs defaultValue="pending" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="pending">
                Pending Approval ({pendingDoctors.length})
              </TabsTrigger>
              <TabsTrigger value="approved">
                Approved ({approvedDoctors.length})
              </TabsTrigger>
              <TabsTrigger value="rejected">
                Rejected ({rejectedDoctors.length})
              </TabsTrigger>
            </TabsList>

            {/* Pending Doctors Tab */}
            <TabsContent value="pending" className="space-y-4">
              {/* Error State */}
              {pendingError && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="flex items-center justify-between">
                    <span>Failed to load pending doctors. {pendingError.message}</span>
                    <Button variant="outline" size="sm" onClick={handleRetry}>
                      <RefreshCw className="h-4 w-4 mr-1" />
                      Retry
                    </Button>
                  </AlertDescription>
                </Alert>
              )}

              {/* Loading State */}
              {pendingLoading && (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <Skeleton className="h-6 w-48" />
                            <Skeleton className="h-4 w-64" />
                          </div>
                          <Skeleton className="h-6 w-24" />
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <Skeleton className="h-12 w-full" />
                          <Skeleton className="h-12 w-full" />
                          <Skeleton className="h-12 w-full" />
                        </div>
                        <div className="flex justify-between items-center pt-4">
                          <Skeleton className="h-4 w-32" />
                          <div className="space-x-2">
                            <Skeleton className="h-8 w-24 inline-block" />
                            <Skeleton className="h-8 w-20 inline-block" />
                            <Skeleton className="h-8 w-24 inline-block" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Pending Doctors List */}
              {!pendingLoading && !pendingError && (
                <>
                  {pendingDoctors.length > 0 ? (
                    pendingDoctors.map((doctor) => (
                      <Card key={doctor.id} className="hover:shadow-lg transition-shadow">
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-lg">{doctor.name}</CardTitle>
                              <CardDescription>
                                <div className="flex items-center gap-2">
                                  <Users className="h-4 w-4" />
                                  {doctor.email}
                                </div>
                              </CardDescription>
                            </div>
                            <Badge className="bg-yellow-100 text-yellow-800">
                              Pending Review
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <Label className="text-sm font-medium">Phone Number</Label>
                              <p className="text-sm text-gray-600">{doctor.phoneNumber}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium">Gender</Label>
                              <p className="text-sm text-gray-600">{doctor.gender}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium">Status</Label>
                              <p className="text-sm text-gray-600">{doctor.status?.replace('_', ' ') || 'Unknown'}</p>
                            </div>
                          </div>

                          <div>
                            <Label className="text-sm font-medium">Address</Label>
                            <p className="text-sm text-gray-600">{doctor.address}</p>
                          </div>

                          <div className="flex items-center justify-between pt-4 border-t">
                            <p className="text-sm text-gray-600">
                              Submitted: {new Date(doctor.createdAt).toLocaleDateString()}
                            </p>
                            <div className="space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleViewDetails(doctor)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View Details
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleReject(doctor.id)}
                                disabled={rejectMutation.isPending || approveMutation.isPending}
                                className="text-red-600 hover:text-red-700"
                              >
                                {rejectMutation.isPending ? (
                                  <>Rejecting...</>
                                ) : (
                                  <>
                                    <X className="h-4 w-4 mr-1" />
                                    Reject
                                  </>
                                )}
                              </Button>
                              <Button
                                size="sm"
                                onClick={() => handleApprove(doctor.id)}
                                disabled={approveMutation.isPending || rejectMutation.isPending}
                              >
                                {approveMutation.isPending ? (
                                  <>Approving...</>
                                ) : (
                                  <>
                                    <Check className="h-4 w-4 mr-1" />
                                    Approve
                                  </>
                                )}
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center py-8">
                          <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600 text-lg">No pending doctor applications</p>
                          <p className="text-gray-500 text-sm mt-2">
                            All doctor applications have been reviewed.
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </>
              )}
            </TabsContent>

            {/* Approved Doctors Tab */}
            <TabsContent value="approved" className="space-y-4">
              {usersLoading ? (
                <div className="space-y-4">
                  {[1, 2].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <Skeleton className="h-6 w-48" />
                            <Skeleton className="h-4 w-64" />
                          </div>
                          <Skeleton className="h-6 w-20" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-between items-center">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-8 w-24" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : approvedDoctors.length > 0 ? (
                approvedDoctors.map((doctor) => (
                  <Card key={doctor.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg">{doctor.name}</CardTitle>
                          <CardDescription>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4" />
                              {doctor.email}
                            </div>
                          </CardDescription>
                        </div>
                        <Badge className="bg-green-100 text-green-800">
                          Approved
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <p className="text-sm text-gray-600">
                            Approved: {new Date(doctor.updatedAt).toLocaleDateString()}
                          </p>
                          <p className="text-sm text-gray-500">
                            Phone: {doctor.phoneNumber}
                          </p>
                        </div>
                        <div className="space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDetails(doctor)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Profile
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleReject(doctor.id)}
                            disabled={rejectMutation.isPending || approveMutation.isPending}
                            className="text-red-600 hover:text-red-700"
                          >
                            {rejectMutation.isPending ? (
                              <>Rejecting...</>
                            ) : (
                              <>
                                <X className="h-4 w-4 mr-1" />
                                Reject
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <Check className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 text-lg">No approved doctors yet</p>
                      <p className="text-gray-500 text-sm mt-2">
                        Approved doctors will appear here.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Rejected Doctors Tab */}
            <TabsContent value="rejected" className="space-y-4">
              {usersLoading ? (
                <div className="space-y-4">
                  {[1].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <Skeleton className="h-6 w-48" />
                            <Skeleton className="h-4 w-64" />
                          </div>
                          <Skeleton className="h-6 w-20" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-between items-center">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-8 w-24" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : rejectedDoctors.length > 0 ? (
                rejectedDoctors.map((doctor) => (
                  <Card key={doctor.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg">{doctor.name}</CardTitle>
                          <CardDescription>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4" />
                              {doctor.email}
                            </div>
                          </CardDescription>
                        </div>
                        <Badge className="bg-red-100 text-red-800">
                          Rejected
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <p className="text-sm text-gray-600">
                            Rejected: {new Date(doctor.updatedAt).toLocaleDateString()}
                          </p>
                          <p className="text-sm text-gray-500">
                            Phone: {doctor.phoneNumber}
                          </p>
                        </div>
                        <div className="space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDetails(doctor)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleApprove(doctor.id)}
                            disabled={approveMutation.isPending || rejectMutation.isPending}
                            className="text-green-600 hover:text-green-700"
                          >
                            {approveMutation.isPending ? (
                              <>Approving...</>
                            ) : (
                              <>
                                <Check className="h-4 w-4 mr-1" />
                                Re-approve
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <X className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 text-lg">No rejected applications</p>
                      <p className="text-gray-500 text-sm mt-2">
                        Rejected doctor applications will appear here.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          {/* Doctor Details Modal */}
          <DoctorDetailsModal
            doctor={selectedDoctor}
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            onApprove={handleApprove}
            onReject={handleReject}
            isApproving={approveMutation.isPending}
            isRejecting={rejectMutation.isPending}
          />
        </div>
      </div>
    </div>
  );
};

export default DoctorApproval;
