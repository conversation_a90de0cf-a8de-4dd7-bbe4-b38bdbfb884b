
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Search,
  Plus,
  Shield,
  UserCheck,
  UserX,
  Users,
  AlertCircle,
  RefreshCw,
  Phone,
  MapPin,
  Calendar
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  useAllUsers,
  useCreateUser,
  useUpdateUserRole,
  useActivateUser,
  useDeactivateUser
} from "@/hooks/useAdmin";
import { User } from "@/types/api";
import EditRoleModal from "@/components/admin/EditRoleModal";
import AddUserModal, { CreateUserData } from "@/components/admin/AddUserModal";
import Header from "@/components/common/Header";

// UserCard component
const UserCard = ({
  user,
  onEditRole,
  onActivate,
  onDeactivate,
  getRoleColor,
  getStatusColor,
  isUpdating
}: {
  user: User;
  onEditRole: (user: User) => void;
  onActivate: (id: number, name: string) => void;
  onDeactivate: (id: number, name: string) => void;
  getRoleColor: (role: string) => string;
  getStatusColor: (status: string) => string;
  isUpdating: boolean;
}) => (
  <Card className="hover:shadow-lg transition-shadow">
    <CardHeader>
      <div className="flex items-center justify-between">
        <div>
          <CardTitle className="text-lg">{user.name}</CardTitle>
          <CardDescription className="flex items-center gap-4 mt-1">
            <span>{user.email}</span>
            <span className="flex items-center gap-1">
              <Phone className="h-3 w-3" />
              {user.phoneNumber}
            </span>
          </CardDescription>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className={getRoleColor(user.role)}>
            {user.role}
          </Badge>
          <Badge className={getStatusColor(user.status)}>
            {user.status.replace('_', ' ')}
          </Badge>
        </div>
      </div>
    </CardHeader>
    <CardContent>
      <div className="space-y-3">
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span className="flex items-center gap-1">
            <MapPin className="h-3 w-3" />
            {user.address}
          </span>
          <span className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            Joined: {new Date(user.createdAt).toLocaleDateString()}
          </span>
        </div>

        <div className="flex items-center justify-between pt-3 border-t">
          <div className="text-sm text-gray-500">
            Last login: {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
          </div>
          <div className="space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => onEditRole(user)}
              disabled={isUpdating}
            >
              <Shield className="h-4 w-4 mr-1" />
              Edit Role
            </Button>
            {user.status === 'ACTIVE' ? (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onDeactivate(user.id, user.name)}
                disabled={isUpdating}
                className="text-red-600 hover:text-red-700"
              >
                <UserX className="h-4 w-4 mr-1" />
                Deactivate
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={() => onActivate(user.id, user.name)}
                disabled={isUpdating}
                className="text-green-600 hover:text-green-700"
              >
                <UserCheck className="h-4 w-4 mr-1" />
                Activate
              </Button>
            )}
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);

const UserManagement = () => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);

  // Fetch data using React Query hooks
  const {
    data: users = [],
    isLoading,
    error,
    refetch
  } = useAllUsers();

  // Mutations
  const createUserMutation = useCreateUser();
  const updateRoleMutation = useUpdateUserRole();
  const activateUserMutation = useActivateUser();
  const deactivateUserMutation = useDeactivateUser();

  // Filter users based on search term
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter users by role and status
  const activeUsers = users.filter(user => user.status === 'ACTIVE');
  const doctorUsers = users.filter(user => user.role === 'DOCTOR' && user.status === 'ACTIVE');
  const patientUsers = users.filter(user => user.role === 'PATIENT' && user.status === 'ACTIVE');
  const clinicUsers = users.filter(user => user.role === 'CLINIC' && user.status === 'ACTIVE');
  const pendingUsers = users.filter(user =>
    user.status === 'PENDING_APPROVAL' || user.status === 'PENDING'
  );
  const inactiveUsers = users.filter(user =>
    user.status === 'INACTIVE' || user.status === 'SUSPENDED'
  );

  // Handler functions
  const handleEditRole = (user: User) => {
    setSelectedUser(user);
    setIsRoleModalOpen(true);
  };

  const handleCreateUser = async (userData: CreateUserData) => {
    try {
      const newUser = await createUserMutation.mutateAsync(userData);

      // Determine the expected status after creation
      const statusAfterCreation = ['DOCTOR', 'CLINIC'].includes(userData.role) ? 'PENDING_APPROVAL' : 'ACTIVE';

      toast({
        title: "User Created Successfully",
        description: `${newUser.name} has been created with role ${userData.role}. ${
          statusAfterCreation === 'PENDING_APPROVAL'
            ? 'Status set to PENDING_APPROVAL - requires admin approval.'
            : 'User has immediate access with their assigned permissions.'
        }`,
      });
      setIsAddUserModalOpen(false);
    } catch (error: any) {
      // Handle specific error cases
      let errorMessage = "Failed to create user. Please try again.";

      if (error.message?.includes('Email already exists')) {
        errorMessage = "Email already exists. Please use a different email address.";
      } else if (error.message?.includes('invalid email')) {
        errorMessage = "Please enter a valid email address.";
      } else if (error.message?.includes('password')) {
        errorMessage = "Password must be at least 6 characters long.";
      } else if (error.message?.includes('authorization')) {
        errorMessage = "You don't have permission to create users.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "User Creation Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleUpdateRole = async (userId: number, newRole: string, reason?: string) => {
    try {
      const user = users.find(u => u.id === userId);
      const oldRole = user?.role;

      await updateRoleMutation.mutateAsync({ id: userId, role: newRole, reason });

      // Determine the expected status after role change
      const statusAfterChange = ['DOCTOR', 'CLINIC'].includes(newRole) ? 'PENDING_APPROVAL' : 'ACTIVE';

      toast({
        title: "Role Updated Successfully",
        description: `User role changed from ${oldRole} to ${newRole}. ${
          statusAfterChange === 'PENDING_APPROVAL'
            ? 'Status set to PENDING_APPROVAL - requires admin approval.'
            : 'User has immediate access with new permissions.'
        }${reason ? ` Reason: ${reason}` : ''}`,
      });
      setIsRoleModalOpen(false);
    } catch (error: any) {
      // Handle specific error cases
      let errorMessage = "Failed to update user role. Please try again.";

      if (error.message?.includes('same role')) {
        errorMessage = "User already has this role. No changes needed.";
      } else if (error.message?.includes('invalid role')) {
        errorMessage = "Invalid role selected. Please choose a valid role.";
      } else if (error.message?.includes('not found')) {
        errorMessage = "User not found. Please refresh and try again.";
      } else if (error.message?.includes('authorization')) {
        errorMessage = "You don't have permission to change user roles.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Role Update Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleActivateUser = async (userId: number, userName: string) => {
    try {
      await activateUserMutation.mutateAsync(userId);
      toast({
        title: "User Activated",
        description: `${userName} has been activated successfully.`,
      });
    } catch (error: any) {
      toast({
        title: "Activation Failed",
        description: error.message || "Failed to activate user. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeactivateUser = async (userId: number, userName: string) => {
    try {
      await deactivateUserMutation.mutateAsync(userId);
      toast({
        title: "User Deactivated",
        description: `${userName} has been deactivated successfully.`,
      });
    } catch (error: any) {
      toast({
        title: "Deactivation Failed",
        description: error.message || "Failed to deactivate user. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRetry = () => {
    refetch();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-purple-100 text-purple-800';
      case 'DOCTOR': return 'bg-blue-100 text-blue-800';
      case 'CLINIC': return 'bg-green-100 text-green-800';
      case 'CLINIC_STAFF': return 'bg-teal-100 text-teal-800';
      case 'PATIENT': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'PENDING_APPROVAL':
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'INACTIVE':
      case 'SUSPENDED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="pt-20 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
                <p className="text-gray-600">Manage all system users and their permissions</p>
              </div>
              <Button
                onClick={() => setIsAddUserModalOpen(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mt-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Users</p>
                      {isLoading ? (
                        <Skeleton className="h-8 w-16" />
                      ) : (
                        <p className="text-2xl font-bold text-blue-600">{users.length}</p>
                      )}
                    </div>
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Active</p>
                      {isLoading ? (
                        <Skeleton className="h-8 w-16" />
                      ) : (
                        <p className="text-2xl font-bold text-green-600">{activeUsers.length}</p>
                      )}
                    </div>
                    <UserCheck className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pending</p>
                      {isLoading ? (
                        <Skeleton className="h-8 w-16" />
                      ) : (
                        <p className="text-2xl font-bold text-yellow-600">{pendingUsers.length}</p>
                      )}
                    </div>
                    <AlertCircle className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Inactive</p>
                      {isLoading ? (
                        <Skeleton className="h-8 w-16" />
                      ) : (
                        <p className="text-2xl font-bold text-red-600">{inactiveUsers.length}</p>
                      )}
                    </div>
                    <UserX className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Doctors</p>
                      {isLoading ? (
                        <Skeleton className="h-8 w-16" />
                      ) : (
                        <p className="text-2xl font-bold text-purple-600">{doctorUsers.length}</p>
                      )}
                    </div>
                    <UserCheck className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Search Bar */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search users by name, email, or role..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Error State */}
          {error && (
            <Alert className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Failed to load users. {error.message}</span>
                <Button variant="outline" size="sm" onClick={handleRetry}>
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Retry
                </Button>
              </AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="all" className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="all">All Users ({filteredUsers.length})</TabsTrigger>
              <TabsTrigger value="active">Active ({activeUsers.length})</TabsTrigger>
              <TabsTrigger value="doctors">Doctors ({doctorUsers.length})</TabsTrigger>
              <TabsTrigger value="patients">Patients ({patientUsers.length})</TabsTrigger>
              <TabsTrigger value="pending">Pending ({pendingUsers.length})</TabsTrigger>
              <TabsTrigger value="inactive">Inactive ({inactiveUsers.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-4">
              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <Skeleton className="h-6 w-48" />
                            <Skeleton className="h-4 w-64" />
                          </div>
                          <div className="space-x-2">
                            <Skeleton className="h-6 w-20" />
                            <Skeleton className="h-6 w-16" />
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-between items-center">
                          <Skeleton className="h-4 w-32" />
                          <div className="space-x-2">
                            <Skeleton className="h-8 w-24 inline-block" />
                            <Skeleton className="h-8 w-24 inline-block" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <UserCard
                    key={user.id}
                    user={user}
                    onEditRole={handleEditRole}
                    onActivate={handleActivateUser}
                    onDeactivate={handleDeactivateUser}
                    getRoleColor={getRoleColor}
                    getStatusColor={getStatusColor}
                    isUpdating={
                      updateRoleMutation.isPending ||
                      activateUserMutation.isPending ||
                      deactivateUserMutation.isPending
                    }
                  />
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 text-lg">No users found</p>
                      <p className="text-gray-500 text-sm mt-2">
                        {searchTerm ? "Try adjusting your search criteria." : "No users available."}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="active" className="space-y-4">
              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <Skeleton className="h-6 w-48" />
                            <Skeleton className="h-4 w-64" />
                          </div>
                          <div className="space-x-2">
                            <Skeleton className="h-6 w-20" />
                            <Skeleton className="h-6 w-16" />
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-between items-center">
                          <Skeleton className="h-4 w-32" />
                          <div className="space-x-2">
                            <Skeleton className="h-8 w-24 inline-block" />
                            <Skeleton className="h-8 w-24 inline-block" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : activeUsers.length > 0 ? (
                activeUsers.map((user) => (
                  <UserCard
                    key={user.id}
                    user={user}
                    onEditRole={handleEditRole}
                    onActivate={handleActivateUser}
                    onDeactivate={handleDeactivateUser}
                    getRoleColor={getRoleColor}
                    getStatusColor={getStatusColor}
                    isUpdating={
                      updateRoleMutation.isPending ||
                      activateUserMutation.isPending ||
                      deactivateUserMutation.isPending
                    }
                  />
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <UserCheck className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 text-lg">No active users found</p>
                      <p className="text-gray-500 text-sm mt-2">
                        All users are either pending approval or inactive.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="doctors" className="space-y-4">
              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-64" />
                      </CardHeader>
                      <CardContent>
                        <Skeleton className="h-4 w-32" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : doctorUsers.length > 0 ? (
                doctorUsers.map((user) => (
                  <UserCard
                    key={user.id}
                    user={user}
                    onEditRole={handleEditRole}
                    onActivate={handleActivateUser}
                    onDeactivate={handleDeactivateUser}
                    getRoleColor={getRoleColor}
                    getStatusColor={getStatusColor}
                    isUpdating={
                      updateRoleMutation.isPending ||
                      activateUserMutation.isPending ||
                      deactivateUserMutation.isPending
                    }
                  />
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <UserCheck className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 text-lg">No doctors found</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="patients" className="space-y-4">
              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-64" />
                      </CardHeader>
                      <CardContent>
                        <Skeleton className="h-4 w-32" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : patientUsers.length > 0 ? (
                patientUsers.map((user) => (
                  <UserCard
                    key={user.id}
                    user={user}
                    onEditRole={handleEditRole}
                    onActivate={handleActivateUser}
                    onDeactivate={handleDeactivateUser}
                    getRoleColor={getRoleColor}
                    getStatusColor={getStatusColor}
                    isUpdating={
                      updateRoleMutation.isPending ||
                      activateUserMutation.isPending ||
                      deactivateUserMutation.isPending
                    }
                  />
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 text-lg">No patients found</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="inactive" className="space-y-4">
              {isLoading ? (
                <div className="space-y-4">
                  {[1].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-64" />
                      </CardHeader>
                      <CardContent>
                        <Skeleton className="h-4 w-32" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : inactiveUsers.length > 0 ? (
                inactiveUsers.map((user) => (
                  <UserCard
                    key={user.id}
                    user={user}
                    onEditRole={handleEditRole}
                    onActivate={handleActivateUser}
                    onDeactivate={handleDeactivateUser}
                    getRoleColor={getRoleColor}
                    getStatusColor={getStatusColor}
                    isUpdating={
                      updateRoleMutation.isPending ||
                      activateUserMutation.isPending ||
                      deactivateUserMutation.isPending
                    }
                  />
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <UserX className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 text-lg">No inactive users found</p>
                      <p className="text-gray-500 text-sm mt-2">
                        All users are currently active or pending approval.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="pending" className="space-y-4">
              {isLoading ? (
                <div className="space-y-4">
                  {[1].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-64" />
                      </CardHeader>
                      <CardContent>
                        <Skeleton className="h-4 w-32" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : pendingUsers.length > 0 ? (
                pendingUsers.map((user) => (
                  <UserCard
                    key={user.id}
                    user={user}
                    onEditRole={handleEditRole}
                    onActivate={handleActivateUser}
                    onDeactivate={handleDeactivateUser}
                    getRoleColor={getRoleColor}
                    getStatusColor={getStatusColor}
                    isUpdating={
                      updateRoleMutation.isPending ||
                      activateUserMutation.isPending ||
                      deactivateUserMutation.isPending
                    }
                  />
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <AlertCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 text-lg">No pending approvals</p>
                      <p className="text-gray-500 text-sm mt-2">
                        All users have been reviewed.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          {/* Edit Role Modal */}
          <EditRoleModal
            user={selectedUser}
            isOpen={isRoleModalOpen}
            onClose={() => setIsRoleModalOpen(false)}
            onSave={handleUpdateRole}
            isLoading={updateRoleMutation.isPending}
          />

          {/* Add User Modal */}
          <AddUserModal
            isOpen={isAddUserModalOpen}
            onClose={() => setIsAddUserModalOpen(false)}
            onSave={handleCreateUser}
            isLoading={createUserMutation.isPending}
          />
        </div>
      </div>
    </div>
  );
};

export default UserManagement;
