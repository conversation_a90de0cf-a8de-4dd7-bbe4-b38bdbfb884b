import { api } from './api';

// Types for database entities
export interface DatabaseAppointment {
  id: number;
  patientId: number;
  doctorId: number;
  clinicId: number;
  appointmentDate: string;
  status: 'SCHEDULED' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  reason: string;
  notes: string;
  durationMinutes: number;
  createdAt: string;
  updatedAt: string;
  cancelledAt?: string;
  cancellationReason?: string;
  // Joined data from related tables
  patientName: string;
  patientEmail: string;
  patientBloodGroup?: string;
  patientAllergies?: string;
  doctorName: string;
  clinicName: string;
}

export interface AppointmentStatistics {
  totalAppointments: number;
  todayAppointments: number;
  upcomingAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  completionRate: number;
  averageDuration: number;
  busyHours: { hour: number; count: number }[];
}

export interface AvailableSlot {
  time: string;
  available: boolean;
  reason?: string;
}

export interface UpdateAppointmentRequest {
  status?: string;
  notes?: string;
  reason?: string;
  appointmentDate?: string;
  durationMinutes?: number;
}

export interface RescheduleRequest {
  appointmentDate: string;
  notes?: string;
}

export interface CancelRequest {
  reason: string;
}

// Doctor Appointments Database Service
export const doctorAppointmentsService = {
  // ✅ Get today's appointments from database
  async getTodayAppointments(doctorId: number): Promise<DatabaseAppointment[]> {
    console.log('🚀 Fetching today\'s appointments for doctor:', doctorId);
    
    const response = await api.get(`/doctor/${doctorId}/appointments/today`);
    console.log('✅ Today\'s appointments fetched successfully:', response);
    
    return response.data || [];
  },

  // ✅ Get upcoming appointments from database
  async getUpcomingAppointments(doctorId: number): Promise<DatabaseAppointment[]> {
    console.log('🚀 Fetching upcoming appointments for doctor:', doctorId);
    
    const response = await api.get(`/doctor/${doctorId}/appointments/upcoming`);
    console.log('✅ Upcoming appointments fetched successfully:', response);
    
    return response.data || [];
  },

  // ✅ Get completed appointments from database
  async getCompletedAppointments(doctorId: number): Promise<DatabaseAppointment[]> {
    console.log('🚀 Fetching completed appointments for doctor:', doctorId);
    
    const response = await api.get(`/doctor/${doctorId}/appointments/completed`);
    console.log('✅ Completed appointments fetched successfully:', response);
    
    return response.data || [];
  },

  // ✅ Get all doctor's appointments from database
  async getAllAppointments(doctorId: number, status?: string, date?: string): Promise<DatabaseAppointment[]> {
    console.log('🚀 Fetching all appointments for doctor:', doctorId, { status, date });

    const params = new URLSearchParams();
    if (status) params.append('status', status);
    if (date) params.append('date', date);

    const response = await api.get(`/appointments/doctor/${doctorId}?${params.toString()}`);
    console.log('✅ All appointments fetched successfully:', response);

    return response.data || [];
  },

  // ✅ Get appointment by ID from database
  async getAppointmentById(appointmentId: number): Promise<DatabaseAppointment> {
    console.log('🚀 Fetching appointment by ID:', appointmentId);

    const response = await api.get(`/appointments/${appointmentId}`);
    console.log('✅ Appointment fetched successfully:', response);

    return response.data;
  },

  // ✅ Get patient's appointments from database
  async getPatientAppointments(patientId: number): Promise<DatabaseAppointment[]> {
    console.log('🚀 Fetching appointments for patient:', patientId);

    const response = await api.get(`/appointments/patient/${patientId}`);
    console.log('✅ Patient appointments fetched successfully:', response);

    return response.data || [];
  },

  // ✅ Update appointment status in database
  async updateAppointmentStatus(appointmentId: number, status: string): Promise<{ success: boolean; message: string }> {
    console.log('🚀 Updating appointment status:', { appointmentId, status });

    const response = await api.put(`/appointments/${appointmentId}/status?status=${status}`);
    console.log('✅ Appointment status updated successfully:', response);

    return {
      success: true,
      message: response.message || 'Appointment status updated successfully'
    };
  },

  // ✅ Update appointment notes in database
  async updateAppointmentNotes(appointmentId: number, notes: string): Promise<{ success: boolean; message: string }> {
    console.log('🚀 Updating appointment notes:', { appointmentId, notes });

    const response = await api.put(`/appointments/${appointmentId}`, { notes });
    console.log('✅ Appointment notes updated successfully:', response);

    return {
      success: true,
      message: response.message || 'Appointment notes updated successfully'
    };
  },

  // ✅ Reschedule appointment in database
  async rescheduleAppointment(appointmentId: number, appointmentDate: string, notes?: string): Promise<{ success: boolean; message: string }> {
    console.log('🚀 Rescheduling appointment:', { appointmentId, appointmentDate, notes });

    const response = await api.put(`/appointments/${appointmentId}`, {
      appointmentDate,
      notes
    });
    console.log('✅ Appointment rescheduled successfully:', response);

    return {
      success: true,
      message: response.message || 'Appointment rescheduled successfully'
    };
  },

  // ✅ Cancel appointment in database
  async cancelAppointment(appointmentId: number, reason: string): Promise<{ success: boolean; message: string }> {
    console.log('🚀 Cancelling appointment:', { appointmentId, reason });

    const response = await api.put(`/appointments/${appointmentId}/cancel?reason=${encodeURIComponent(reason)}`);
    console.log('✅ Appointment cancelled successfully:', response);

    return {
      success: true,
      message: response.message || 'Appointment cancelled successfully'
    };
  },

  // ✅ Get doctor's available time slots from database
  async getAvailableSlots(doctorId: number, date: string): Promise<AvailableSlot[]> {
    console.log('🚀 Fetching available slots for doctor:', { doctorId, date });

    const response = await api.get(`/appointments/doctor/${doctorId}/availability?date=${date}`);
    console.log('✅ Available slots fetched successfully:', response);

    return response.data || [];
  },

  // ✅ Get appointment statistics from database (optional - may not be implemented yet)
  async getAppointmentStatistics(doctorId: number): Promise<AppointmentStatistics | null> {
    console.log('🚀 Fetching appointment statistics for doctor:', doctorId);

    try {
      const response = await api.get(`/doctor/${doctorId}/appointments/statistics`);
      console.log('✅ Appointment statistics fetched successfully:', response);

      return response.data;
    } catch (error: any) {
      console.log('⚠️ Appointment statistics endpoint not available yet:', error.message);
      // Return null if endpoint doesn't exist yet - this is optional functionality
      return null;
    }
  },

  // ✅ Complete appointment with diagnosis
  async completeAppointmentWithDiagnosis(appointmentId: number, diagnosisData: {
    diagnosisCode: string;
    diagnosisDescription: string;
    symptoms: string;
    treatmentPlan: string;
    notes?: string;
  }): Promise<{ success: boolean; message: string }> {
    console.log('🚀 Completing appointment with diagnosis:', { appointmentId, diagnosisData });
    
    // First update appointment status
    await this.updateAppointmentStatus(appointmentId, 'COMPLETED');
    
    // Then add diagnosis
    const response = await api.post(`/appointments/${appointmentId}/diagnosis`, diagnosisData);
    console.log('✅ Appointment completed with diagnosis:', response);
    
    return {
      success: true,
      message: response.message || 'Appointment completed with diagnosis'
    };
  }
};
