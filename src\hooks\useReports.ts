import { useQuery } from '@tanstack/react-query';
import { reportsService, SystemStats, UserGrowthData, AppointmentAnalytics, PerformanceMetrics, RevenueAnalytics } from '@/services/reportsService';

// Query keys for reports
export const reportsKeys = {
  all: ['reports'] as const,
  systemStats: (days?: number) => [...reportsKeys.all, 'system-stats', days] as const,
  userGrowth: (months?: number) => [...reportsKeys.all, 'user-growth', months] as const,
  appointmentAnalytics: (days?: number) => [...reportsKeys.all, 'appointment-analytics', days] as const,
  performanceMetrics: () => [...reportsKeys.all, 'performance-metrics'] as const,
  revenueAnalytics: (days?: number) => [...reportsKeys.all, 'revenue-analytics', days] as const,
};

// System Statistics Hook
export const useSystemStats = (days: number = 30) => {
  return useQuery({
    queryKey: reportsKeys.systemStats(days),
    queryFn: () => reportsService.getSystemStats(days),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// User Growth Data Hook
export const useUserGrowthData = (months: number = 12) => {
  return useQuery({
    queryKey: reportsKeys.userGrowth(months),
    queryFn: () => reportsService.getUserGrowthData(months),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Appointment Analytics Hook
export const useAppointmentAnalytics = (days: number = 30) => {
  return useQuery({
    queryKey: reportsKeys.appointmentAnalytics(days),
    queryFn: () => reportsService.getAppointmentAnalytics(days),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Performance Metrics Hook
export const usePerformanceMetrics = () => {
  return useQuery({
    queryKey: reportsKeys.performanceMetrics(),
    queryFn: () => reportsService.getPerformanceMetrics(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Revenue Analytics Hook
export const useRevenueAnalytics = (days: number = 30) => {
  return useQuery({
    queryKey: reportsKeys.revenueAnalytics(days),
    queryFn: () => reportsService.getRevenueAnalytics(days),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Combined hook for dashboard overview
export const useReportsDashboard = (days: number = 30) => {
  const systemStats = useSystemStats(days);
  const userGrowth = useUserGrowthData(6); // Last 6 months
  const appointmentAnalytics = useAppointmentAnalytics(days);
  const performanceMetrics = usePerformanceMetrics();

  return {
    systemStats,
    userGrowth,
    appointmentAnalytics,
    performanceMetrics,
    isLoading: systemStats.isLoading || userGrowth.isLoading || appointmentAnalytics.isLoading || performanceMetrics.isLoading,
    isError: systemStats.isError || userGrowth.isError || appointmentAnalytics.isError || performanceMetrics.isError,
    error: systemStats.error || userGrowth.error || appointmentAnalytics.error || performanceMetrics.error,
  };
};
