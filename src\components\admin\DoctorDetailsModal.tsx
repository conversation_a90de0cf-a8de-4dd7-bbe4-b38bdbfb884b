import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  FileText, 
  Award, 
  Clock, 
  DollarSign,
  Building,
  Check,
  X
} from "lucide-react";
import { User as UserType } from "@/types/api";

interface DoctorDetailsModalProps {
  doctor: UserType | null;
  isOpen: boolean;
  onClose: () => void;
  onApprove: (id: number) => void;
  onReject: (id: number) => void;
  isApproving?: boolean;
  isRejecting?: boolean;
}

const DoctorDetailsModal = ({ 
  doctor, 
  isOpen, 
  onClose, 
  onApprove, 
  onReject,
  isApproving = false,
  isRejecting = false
}: DoctorDetailsModalProps) => {
  if (!doctor) return null;

  // Mock additional doctor details that would come from API
  const doctorDetails = {
    medicalLicense: "MD789012",
    specialty: "Pediatrics",
    yearsOfExperience: 8,
    qualification: "MBBS, MD Pediatrics",
    consultationFee: 150,
    bio: "Experienced pediatrician with a passion for child healthcare and development. Specialized in treating common childhood illnesses and providing preventive care.",
    clinicName: "Children's Medical Center",
    documents: [
      { name: "Medical License", status: "verified", uploadedAt: "2024-01-15" },
      { name: "Medical Degree", status: "verified", uploadedAt: "2024-01-15" },
      { name: "Experience Certificate", status: "pending", uploadedAt: "2024-01-16" },
      { name: "Identity Proof", status: "verified", uploadedAt: "2024-01-14" }
    ],
    references: [
      { name: "Dr. Sarah Johnson", position: "Chief of Pediatrics", hospital: "City General Hospital" },
      { name: "Dr. Michael Brown", position: "Senior Pediatrician", hospital: "Children's Hospital" }
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Doctor Application Details
          </DialogTitle>
          <DialogDescription>
            Review the complete application for {doctor.name}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh] pr-4">
          <div className="space-y-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Personal Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <User className="h-4 w-4" />
                    Full Name
                  </Label>
                  <p className="text-sm text-gray-700">{doctor.name}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <Mail className="h-4 w-4" />
                    Email Address
                  </Label>
                  <p className="text-sm text-gray-700">{doctor.email}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <Phone className="h-4 w-4" />
                    Phone Number
                  </Label>
                  <p className="text-sm text-gray-700">{doctor.phoneNumber}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    Date of Birth
                  </Label>
                  <p className="text-sm text-gray-700">
                    {new Date(doctor.dateOfBirth).toLocaleDateString()}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    Address
                  </Label>
                  <p className="text-sm text-gray-700">{doctor.address}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Gender</Label>
                  <p className="text-sm text-gray-700">{doctor.gender}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Professional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Professional Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    Medical License
                  </Label>
                  <p className="text-sm text-gray-700">{doctorDetails.medicalLicense}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <Award className="h-4 w-4" />
                    Specialty
                  </Label>
                  <p className="text-sm text-gray-700">{doctorDetails.specialty}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    Experience
                  </Label>
                  <p className="text-sm text-gray-700">{doctorDetails.yearsOfExperience} years</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <DollarSign className="h-4 w-4" />
                    Consultation Fee
                  </Label>
                  <p className="text-sm text-gray-700">${doctorDetails.consultationFee}</p>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label className="text-sm font-medium">Qualification</Label>
                  <p className="text-sm text-gray-700">{doctorDetails.qualification}</p>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <Building className="h-4 w-4" />
                    Clinic/Hospital
                  </Label>
                  <p className="text-sm text-gray-700">{doctorDetails.clinicName}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Bio */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Professional Bio</h3>
              <p className="text-sm text-gray-700 leading-relaxed">{doctorDetails.bio}</p>
            </div>

            <Separator />

            {/* Documents */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Submitted Documents</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {doctorDetails.documents.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">{doc.name}</p>
                        <p className="text-xs text-gray-500">Uploaded: {doc.uploadedAt}</p>
                      </div>
                    </div>
                    <Badge className={getStatusColor(doc.status)}>
                      {doc.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* References */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Professional References</h3>
              <div className="space-y-3">
                {doctorDetails.references.map((ref, index) => (
                  <div key={index} className="p-3 border rounded-lg">
                    <p className="text-sm font-medium">{ref.name}</p>
                    <p className="text-xs text-gray-600">{ref.position}</p>
                    <p className="text-xs text-gray-500">{ref.hospital}</p>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Application Status */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Application Status</h3>
              <div className="flex items-center gap-4">
                <Badge className="bg-yellow-100 text-yellow-800">
                  {doctor.status.replace('_', ' ')}
                </Badge>
                <p className="text-sm text-gray-600">
                  Submitted: {new Date(doctor.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </ScrollArea>

        {/* Action Buttons */}
        <div className="flex items-center justify-end gap-3 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button 
            variant="outline" 
            onClick={() => onReject(doctor.id)}
            disabled={isRejecting || isApproving}
            className="text-red-600 hover:text-red-700"
          >
            {isRejecting ? (
              <>Rejecting...</>
            ) : (
              <>
                <X className="h-4 w-4 mr-1" />
                Reject
              </>
            )}
          </Button>
          <Button 
            onClick={() => onApprove(doctor.id)}
            disabled={isApproving || isRejecting}
          >
            {isApproving ? (
              <>Approving...</>
            ) : (
              <>
                <Check className="h-4 w-4 mr-1" />
                Approve
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DoctorDetailsModal;
