import { api } from './api';
import { SystemReport, User } from '@/types/api';

// Mock data store for development
let mockUsers: User[] = [];

// Initialize mock data
const initializeMockData = (): User[] => [
  {
    id: 1,
    name: "Dr. <PERSON>",
    email: "<EMAIL>",
    phoneNumber: "+**********",
    address: "123 Medical Center Dr",
    dateOfBirth: "1985-03-15",
    gender: "FEMALE",
    role: "DOCTOR",
    status: "ACTIVE",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
    lastLogin: "2024-01-20T09:30:00Z"
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phoneNumber: "+**********",
    address: "456 Oak Street",
    dateOfBirth: "1990-07-22",
    gender: "<PERSON><PERSON>",
    role: "PATIENT",
    status: "ACTIVE",
    createdAt: "2024-01-10T14:30:00Z",
    updatedAt: "2024-01-10T14:30:00Z",
    lastLogin: "2024-01-19T16:45:00Z"
  },
  {
    id: 3,
    name: "Central Medical Center",
    email: "<EMAIL>",
    phoneNumber: "+**********",
    address: "789 Healthcare Blvd",
    dateOfBirth: "1980-01-01",
    gender: "OTHER",
    role: "CLINIC",
    status: "PENDING_APPROVAL",
    createdAt: "2024-01-05T11:15:00Z",
    updatedAt: "2024-01-05T11:15:00Z",
    lastLogin: "2024-01-18T08:20:00Z"
  },
  {
    id: 4,
    name: "Dr. Michael Chen",
    email: "<EMAIL>",
    phoneNumber: "+**********",
    address: "321 Health Plaza",
    dateOfBirth: "1982-11-08",
    gender: "MALE",
    role: "DOCTOR",
    status: "PENDING_APPROVAL",
    createdAt: "2024-01-08T09:45:00Z",
    updatedAt: "2024-01-08T09:45:00Z",
    lastLogin: "2024-01-17T13:10:00Z"
  },
  {
    id: 5,
    name: "Emily Davis",
    email: "<EMAIL>",
    phoneNumber: "+**********",
    address: "654 Wellness Ave",
    dateOfBirth: "1995-05-12",
    gender: "FEMALE",
    role: "PATIENT",
    status: "INACTIVE",
    createdAt: "2024-01-12T16:20:00Z",
    updatedAt: "2024-01-12T16:20:00Z",
    lastLogin: "2024-01-16T11:30:00Z"
  },
  {
    id: 6,
    name: "Admin User",
    email: "<EMAIL>",
    phoneNumber: "+**********",
    address: "100 Admin Center",
    dateOfBirth: "1975-12-25",
    gender: "OTHER",
    role: "ADMIN",
    status: "ACTIVE",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    lastLogin: "2024-01-20T10:00:00Z"
  },
  {
    id: 7,
    name: "Sarah Wilson",
    email: "<EMAIL>",
    phoneNumber: "+**********",
    address: "200 Clinic Staff Ave",
    dateOfBirth: "1988-09-14",
    gender: "FEMALE",
    role: "CLINIC_STAFF",
    status: "ACTIVE",
    createdAt: "2024-01-14T12:00:00Z",
    updatedAt: "2024-01-14T12:00:00Z",
    lastLogin: "2024-01-19T14:20:00Z"
  },
  {
    id: 8,
    name: "Dr. Robert Kim",
    email: "<EMAIL>",
    phoneNumber: "+**********",
    address: "300 Medical Plaza",
    dateOfBirth: "1979-04-03",
    gender: "MALE",
    role: "DOCTOR",
    status: "PENDING_APPROVAL",
    createdAt: "2024-01-16T08:30:00Z",
    updatedAt: "2024-01-16T08:30:00Z",
    lastLogin: "2024-01-16T08:30:00Z"
  }
];

export const adminService = {
  // System Reports
  async getSystemOverview(): Promise<SystemReport> {
    return await api.get('/admin/reports/overview');
  },

  // User Management
  async getAllUsers(): Promise<User[]> {
    try {
      console.log('🚀 Fetching users from backend API...');
      const users = await api.get('/admin/users');
      console.log('✅ Users fetched successfully from backend:', users.length, 'users');
      return users;
    } catch (error: any) {
      console.error('❌ Backend API failed for fetching users:', error);

      // Check for authentication errors
      if (error.message?.includes('401') || error.message?.includes('authorization')) {
        throw new Error('You are not authorized to view users. Please login again.');
      }

      if (error.message?.includes('403')) {
        throw new Error('You do not have permission to view users.');
      }

      // For development/testing, fall back to mock data
      console.warn('⚠️ Falling back to mock data for development');
      // Initialize mock data if not already done
      if (mockUsers.length === 0) {
        mockUsers = initializeMockData();
      }
      // Return a copy to prevent direct mutation
      return [...mockUsers];
    }
  },

  async getUserById(id: number): Promise<User> {
    return await api.get(`/admin/users/${id}`);
  },

  async createUser(userData: {
    name: string;
    email: string;
    password: string;
    phoneNumber?: string;
    address?: string;
    dateOfBirth?: string;
    gender: 'MALE' | 'FEMALE' | 'OTHER';
    role: 'PATIENT' | 'DOCTOR' | 'CLINIC' | 'CLINIC_STAFF' | 'ADMIN';
  }): Promise<User> {
    try {
      console.log('🚀 Creating user via backend API:', userData.name);
      const response = await api.post('/admin/users', userData);
      console.log('✅ User created successfully via backend:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Backend API failed for user creation:', error);

      // Check if it's a validation error from backend
      if (error.message?.includes('400') || error.message?.includes('email')) {
        throw new Error('Email already exists or validation failed. Please check your input.');
      }

      if (error.message?.includes('401') || error.message?.includes('authorization')) {
        throw new Error('You are not authorized to create users. Please login again.');
      }

      if (error.message?.includes('403')) {
        throw new Error('You do not have permission to create users.');
      }

      // For development/testing, fall back to mock data
      console.warn('⚠️ Falling back to mock data simulation for development');

      // Initialize mock data if not already done
      if (mockUsers.length === 0) {
        mockUsers = initializeMockData();
      }

      // Validate email uniqueness
      const existingUser = mockUsers.find(user => user.email === userData.email);
      if (existingUser) {
        throw new Error('Email already exists. Please use a different email address.');
      }

      // Determine status based on role
      let status: string;
      if (userData.role === 'DOCTOR' || userData.role === 'CLINIC') {
        status = 'PENDING_APPROVAL';
      } else {
        status = 'ACTIVE';
      }

      // Create new user
      const newUser: User = {
        id: Math.max(...mockUsers.map(u => u.id)) + 1,
        name: userData.name,
        email: userData.email,
        phoneNumber: userData.phoneNumber || '',
        address: userData.address || '',
        dateOfBirth: userData.dateOfBirth || '',
        gender: userData.gender,
        role: userData.role as any,
        status: status as any,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLogin: null
      };

      // Add to mock data
      mockUsers.push(newUser);

      console.log(`✅ Mock User Created: ${newUser.name}`);
      console.log(`   Email: ${newUser.email}`);
      console.log(`   Role: ${newUser.role}`);
      console.log(`   Status: ${newUser.status}`);

      return newUser;
    }
  },

  async updateUserStatus(id: number, status: string): Promise<void> {
    return await api.put(`/admin/users/${id}/status`, { status });
  },

  async deleteUser(id: number): Promise<void> {
    try {
      return await api.delete(`/admin/users/${id}`);
    } catch (error) {
      console.warn('Delete user API not available, simulating success');
      return Promise.resolve();
    }
  },

  async updateUserRole(id: number, role: string, reason?: string): Promise<void> {
    try {
      console.log(`🚀 Updating user role via backend API: User ${id} → ${role}`);
      // Use your backend API endpoint with request body (recommended method)
      const response = await api.put(`/admin/users/${id}/role`, {
        newRole: role,
        reason: reason || `Role change to ${role} - Admin action`
      });
      console.log('✅ Role updated successfully via backend:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Backend API failed for role update:', error);

      // Check for specific backend errors
      if (error.message?.includes('400') || error.message?.includes('same role')) {
        throw new Error(`User already has the role: ${role}`);
      }

      if (error.message?.includes('401') || error.message?.includes('authorization')) {
        throw new Error('You are not authorized to change user roles. Please login again.');
      }

      if (error.message?.includes('403')) {
        throw new Error('You do not have permission to change user roles.');
      }

      if (error.message?.includes('404')) {
        throw new Error('User not found.');
      }

      // For development/testing, fall back to mock data
      console.warn('⚠️ Falling back to mock data simulation for development');

      // Initialize mock data if not already done
      if (mockUsers.length === 0) {
        mockUsers = initializeMockData();
      }

      // Find and update the user in mock data
      const userIndex = mockUsers.findIndex(user => user.id === id);
      if (userIndex !== -1) {
        const user = mockUsers[userIndex];
        const oldRole = user.role;

        // Validate role change
        if (oldRole === role) {
          throw new Error(`User already has the role: ${role}`);
        }

        // Smart status management based on new role (matching your backend logic)
        let newStatus: string;
        if (role === 'DOCTOR' || role === 'CLINIC') {
          newStatus = 'PENDING_APPROVAL';
        } else {
          newStatus = 'ACTIVE';
        }

        // Update the user
        mockUsers[userIndex] = {
          ...user,
          role: role as any,
          status: newStatus as any,
          updatedAt: new Date().toISOString()
        };

        console.log(`✅ Role Change Simulation: ${user.name}`);
        console.log(`   From: ${oldRole} (${user.status}) → To: ${role} (${newStatus})`);
        console.log(`   Reason: ${reason || 'Admin action'}`);
      } else {
        throw new Error('User not found');
      }

      return Promise.resolve();
    }
  },

  // Alternative method using query parameter (as per your backend)
  async updateUserRoleQuery(id: number, role: string): Promise<void> {
    try {
      return await api.put(`/admin/users/${id}/change-role?newRole=${role}`);
    } catch (error) {
      console.warn('Query parameter role change API not available, using fallback');
      // Fall back to the main method
      return this.updateUserRole(id, role);
    }
  },

  async activateUser(id: number): Promise<void> {
    try {
      return await api.put(`/admin/users/${id}/activate`);
    } catch (error) {
      console.warn('Activate user API not available, simulating success');

      // Initialize mock data if not already done
      if (mockUsers.length === 0) {
        mockUsers = initializeMockData();
      }

      // Find and update the user in mock data
      const userIndex = mockUsers.findIndex(user => user.id === id);
      if (userIndex !== -1) {
        const user = mockUsers[userIndex];
        mockUsers[userIndex] = {
          ...user,
          status: 'ACTIVE',
          updatedAt: new Date().toISOString()
        };
        console.log(`✅ User Activated: ${user.name} → Status: ACTIVE`);
      }

      return Promise.resolve();
    }
  },

  async deactivateUser(id: number): Promise<void> {
    try {
      return await api.put(`/admin/users/${id}/deactivate`);
    } catch (error) {
      console.warn('Deactivate user API not available, simulating success');

      // Initialize mock data if not already done
      if (mockUsers.length === 0) {
        mockUsers = initializeMockData();
      }

      // Find and update the user in mock data
      const userIndex = mockUsers.findIndex(user => user.id === id);
      if (userIndex !== -1) {
        const user = mockUsers[userIndex];
        mockUsers[userIndex] = {
          ...user,
          status: 'INACTIVE',
          updatedAt: new Date().toISOString()
        };
        console.log(`✅ User Deactivated: ${user.name} → Status: INACTIVE`);
      }

      return Promise.resolve();
    }
  },

  // Doctor Approvals
  async getPendingDoctors(): Promise<User[]> {
    try {
      return await api.get('/admin/doctors/pending');
    } catch (error) {
      console.warn('Pending doctors API not available, using fallback');
      // Return mock pending doctors as fallback
      return [
        {
          id: 1,
          name: "Dr. Emily Carter",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          address: "123 Medical Center Dr",
          dateOfBirth: "1985-03-15",
          gender: "FEMALE",
          role: "DOCTOR",
          status: "PENDING_APPROVAL",
          createdAt: "2024-01-20T10:00:00Z",
          updatedAt: "2024-01-20T10:00:00Z",
          lastLogin: "2024-01-20T10:00:00Z"
        },
        {
          id: 2,
          name: "Dr. James Wilson",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          address: "456 Health Plaza",
          dateOfBirth: "1980-07-22",
          gender: "MALE",
          role: "DOCTOR",
          status: "PENDING_APPROVAL",
          createdAt: "2024-01-18T14:30:00Z",
          updatedAt: "2024-01-18T14:30:00Z",
          lastLogin: "2024-01-18T14:30:00Z"
        },
        {
          id: 3,
          name: "Dr. Maria Rodriguez",
          email: "<EMAIL>",
          phoneNumber: "+**********",
          address: "789 Care Center Ave",
          dateOfBirth: "1982-11-08",
          gender: "FEMALE",
          role: "DOCTOR",
          status: "PENDING_APPROVAL",
          createdAt: "2024-01-19T09:15:00Z",
          updatedAt: "2024-01-19T09:15:00Z",
          lastLogin: "2024-01-19T09:15:00Z"
        }
      ];
    }
  },

  async approveDoctor(id: number): Promise<void> {
    try {
      return await api.put(`/admin/doctors/${id}/approve`);
    } catch (error) {
      console.warn('Approve doctor API not available, simulating success');
      // Simulate successful approval
      return Promise.resolve();
    }
  },

  async rejectDoctor(id: number): Promise<void> {
    try {
      return await api.put(`/admin/doctors/${id}/reject`);
    } catch (error) {
      console.warn('Reject doctor API not available, simulating success');
      // Simulate successful rejection
      return Promise.resolve();
    }
  },

  // Clinic Approvals
  async getPendingClinics(): Promise<User[]> {
    return await api.get('/admin/clinics/pending');
  },

  async approveClinic(id: number): Promise<void> {
    return await api.put(`/admin/clinics/${id}/approve`);
  },

  async rejectClinic(id: number): Promise<void> {
    return await api.put(`/admin/clinics/${id}/reject`);
  },

  // System Settings
  async getSystemSettings(): Promise<any> {
    return await api.get('/admin/settings');
  },

  async updateSystemSettings(settings: any): Promise<void> {
    return await api.put('/admin/settings', settings);
  },

  // Analytics and Reports
  async getUserAnalytics(period: string = '30d'): Promise<any> {
    return await api.get('/admin/analytics/users', { period });
  },

  async getAppointmentAnalytics(period: string = '30d'): Promise<any> {
    return await api.get('/admin/analytics/appointments', { period });
  },

  async getRevenueAnalytics(period: string = '30d'): Promise<any> {
    return await api.get('/admin/analytics/revenue', { period });
  },

  // Audit Logs
  async getAuditLogs(page: number = 1, limit: number = 50): Promise<any> {
    return await api.get('/admin/audit-logs', { page, limit });
  },

  // System Health
  async getSystemHealth(): Promise<any> {
    return await api.get('/admin/system/health');
  },

  // Backup and Maintenance
  async createBackup(): Promise<any> {
    return await api.post('/admin/system/backup');
  },

  async getBackupHistory(): Promise<any> {
    return await api.get('/admin/system/backups');
  },

  // Notifications Management
  async sendSystemNotification(notification: {
    title: string;
    message: string;
    targetRole?: string;
    targetUsers?: number[];
  }): Promise<void> {
    return await api.post('/admin/notifications/send', notification);
  },

  async getNotificationHistory(): Promise<any> {
    return await api.get('/admin/notifications/history');
  }
};
